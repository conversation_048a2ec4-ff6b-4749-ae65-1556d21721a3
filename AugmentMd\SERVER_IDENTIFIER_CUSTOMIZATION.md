# Server Identifier Customization

## Problem Solved
Changed the server identifier from `server=oatpp/1.3.0` to `server=MiWebServer/1.0.0` in error responses and HTTP headers.

## What Was Changed

### 1. Custom Error Handler
Created a `CustomErrorHandler` class that overrides the default Oat++ error handling:

```cpp
class CustomErrorHandler : public oatpp::web::server::handler::ErrorHandler {
public:
  std::shared_ptr<oatpp::web::protocol::http::outgoing::Response> 
  handleError(const oatpp::web::protocol::http::Status& status, 
              const oatpp::String& message, 
              const Headers& headers) override {
    
    // Custom error response with MiWebServer identifier
    oatpp::data::stream::BufferOutputStream stream;
    stream << "server=MiWebServer/1.0.0\n";  // ✅ Custom server identifier
    stream << "code=" << status.code << "\n";
    stream << "description=" << status.description << "\n";
    stream << "message=" << message << "\n";
    
    auto response = oatpp::web::protocol::http::outgoing::Response::createShared(
      status, 
      oatpp::web::protocol::http::outgoing::BufferBody::createShared(stream.toString())
    );
    
    response->putHeader("Server", "MiWebServer/1.0.0");  // ✅ Custom server header
    // ... rest of implementation
  }
};
```

### 2. Updated Controller
Enhanced the `HelloController` to include custom server headers in all responses:

```cpp
ENDPOINT("GET", "/", root) {
  auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                "Welcome to MiWebServer - Microstack Technology!");
  response->putHeader("Server", "MiWebServer/1.0.0");  // ✅ Custom header
  return response;
}

ENDPOINT("GET", "/health", health) {
  auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
  response->putHeader("Server", "MiWebServer/1.0.0");  // ✅ Custom header
  response->putHeader("Content-Type", "application/json");
  return response;
}
```

### 3. Updated AppComponent
Modified the connection handler to use the custom error handler:

```cpp
OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, serverConnectionHandler)([] {
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);
  auto connectionHandler = oatpp::web::server::HttpConnectionHandler::createShared(router);
  connectionHandler->setErrorHandler(std::make_shared<CustomErrorHandler>());  // ✅ Custom error handler
  return connectionHandler;
}());
```

## Before vs After

### Before (Default Oat++):
```
HTTP/1.1 404 Not Found
Server: oatpp/1.3.0
Content-Type: text/plain

server=oatpp/1.3.0
code=404
description=Not Found
message=No mapping for HTTP-method: 'GET', URL: '/'
```

### After (Custom MiWebServer):
```
HTTP/1.1 404 Not Found
Server: MiWebServer/1.0.0
Content-Type: text/plain

server=MiWebServer/1.0.0
code=404
description=Not Found
message=No mapping for HTTP-method: 'GET', URL: '/nonexistent'
```

## Testing

### Build and Run:
```bash
# Build the project
cmake --build out/build/AMD

# Run the server
./out/build/AMD/MiWebServer.exe
```

### Test Endpoints:
```bash
# Test successful response
curl -I http://localhost:8000/
# Should show: Server: MiWebServer/1.0.0

# Test health endpoint
curl http://localhost:8000/health
# Should return: {"status":"healthy","server":"MiWebServer/1.0.0"}

# Test 404 error
curl http://localhost:8000/nonexistent
# Should show: server=MiWebServer/1.0.0 in error response
```

### Using PowerShell Test Script:
```powershell
# Run the test script
.\test_server.ps1
```

## Key Benefits

1. **Brand Identity**: Server now identifies as "MiWebServer" instead of generic "oatpp"
2. **Version Control**: Easy to update version number in one place
3. **Professional Appearance**: Custom server identifier looks more professional
4. **Consistent Branding**: All responses (success and error) use the same identifier
5. **Security**: Doesn't reveal the underlying framework version

## Customization Options

You can easily change the server identifier by modifying these values:

1. **In CustomErrorHandler**: Change `"MiWebServer/1.0.0"` to your desired identifier
2. **In Controller endpoints**: Update the `Server` header value
3. **Version updates**: Update version number in all locations

## File Structure

```
WebServerApp/
├── main.cpp                     # ✅ Updated with custom error handler and controller
├── test_server.ps1             # 🆕 Test script to verify changes
├── SERVER_IDENTIFIER_CUSTOMIZATION.md  # 🆕 This documentation
└── ... (other files)
```

## Notes

- The custom error handler only affects error responses (404, 500, etc.)
- Successful responses get custom headers through the controller endpoints
- The server identifier appears in both HTTP headers and error response body
- This approach maintains compatibility with the Oat++ framework while providing custom branding

Your server now properly identifies as "MiWebServer/1.0.0" instead of "oatpp/1.3.0"! 🎉
