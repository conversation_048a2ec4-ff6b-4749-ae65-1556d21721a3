# Fix for 'touch' Command Not Found Error

## Problem
The `touch` command is a Unix/Linux command that doesn't exist in Windows PowerShell, causing the error:
```
touch : The term 'touch' is not recognized as the name of a cmdlet, function, script file, or operable program.
```

## Solutions

### Solution 1: Use PowerShell Equivalents (Recommended)

Instead of `touch filename.txt`, use these PowerShell commands:

#### Create a new file:
```powershell
New-Item -ItemType File -Path "filename.txt" -Force
```

#### Update file timestamp:
```powershell
(Get-Item "filename.txt").LastWriteTime = Get-Date
```

#### One-liner for both create and update:
```powershell
if (Test-Path "filename.txt") { (Get-Item "filename.txt").LastWriteTime = Get-Date } else { New-Item -ItemType File -Path "filename.txt" -Force }
```

### Solution 2: Use the Provided touch.ps1 Script

I've created a `touch.ps1` script that provides touch functionality:

```powershell
# Load the touch function
. .\touch.ps1

# Use it like the Unix touch command
touch filename.txt
touch file1.txt file2.txt file3.txt
```

### Solution 3: Use the Provided touch.bat File

For Command Prompt compatibility:

```cmd
touch.bat filename.txt
```

### Solution 4: Add Touch Function to PowerShell Profile

To make touch available in all PowerShell sessions:

1. Open PowerShell as Administrator
2. Run: `notepad $PROFILE` (creates profile if it doesn't exist)
3. Add this function:

```powershell
function touch {
    param([string[]]$Path)
    foreach ($file in $Path) {
        if (Test-Path $file) {
            (Get-Item $file).LastWriteTime = Get-Date
        } else {
            $directory = Split-Path $file -Parent
            if ($directory -and !(Test-Path $directory)) {
                New-Item -ItemType Directory -Path $directory -Force | Out-Null
            }
            New-Item -ItemType File -Path $file -Force | Out-Null
        }
    }
}
```

4. Save and restart PowerShell

### Solution 5: Install Git Bash or WSL

For full Unix command compatibility:

#### Git Bash:
1. Install Git for Windows (includes Git Bash)
2. Use Git Bash terminal instead of PowerShell
3. All Unix commands including `touch` will work

#### WSL (Windows Subsystem for Linux):
1. Install WSL: `wsl --install`
2. Use WSL terminal for Unix commands

## For Your Specific Build Command

If you're running a build command like:
```
cmake -B build -D CMAKE_BUILD_TYPE=Debug ; ninja -C build ; touch d:\MiWebApp\...
```

Replace it with:
```powershell
cmake -B build -D CMAKE_BUILD_TYPE=Debug
ninja -C build
New-Item -ItemType File -Path "d:\MiWebApp\WebServerApp\your-file.txt" -Force
```

Or use the provided touch script:
```powershell
cmake -B build -D CMAKE_BUILD_TYPE=Debug
ninja -C build
.\touch.ps1 "d:\MiWebApp\WebServerApp\your-file.txt"
```

## Quick Fix for Current Session

Run this in your current PowerShell session:
```powershell
function touch { param([string[]]$Path); foreach ($file in $Path) { if (Test-Path $file) { (Get-Item $file).LastWriteTime = Get-Date } else { New-Item -ItemType File -Path $file -Force | Out-Null } } }
```

Now you can use `touch filename.txt` in that session.

## Files Created

I've created these files to help:
- `touch.ps1` - PowerShell touch function
- `touch.bat` - Batch file touch equivalent  
- `clean_build.ps1` - Updated with touch functionality

Choose the solution that best fits your workflow!
