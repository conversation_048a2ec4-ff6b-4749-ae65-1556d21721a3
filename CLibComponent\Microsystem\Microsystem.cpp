/**
 * @file      Microsystem.cpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     Microsystem component implementation
 * @version   0.1
 * @date      22-06-2025
 * @copyright 2025, your company / association / school
 */

#include "Microsystem.hpp"
#include <sstream>
#include <iostream>

Microsystem::Microsystem(const std::string& serviceName, const std::string& version)
    : m_serviceName(serviceName)
    , m_version(version)
    , m_startTime(std::chrono::steady_clock::now())
{
    std::cout << "[Microsystem] Initializing " << m_serviceName << " v" << m_version << std::endl;
}

Microsystem::~Microsystem() {
    std::cout << "[Microsystem] Shutting down " << m_serviceName << std::endl;
}

bool Microsystem::initialize() {
    std::cout << "[Microsystem] " << m_serviceName << " initialized successfully" << std::endl;
    return true;
}

void Microsystem::shutdown() {
    std::cout << "[Microsystem] " << m_serviceName << " shutdown complete" << std::endl;
}

const std::string& Microsystem::getServiceName() const {
    return m_serviceName;
}

const std::string& Microsystem::getVersion() const {
    return m_version;
}

double Microsystem::getUptimeSeconds() const {
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
    return duration.count() / 1000.0;
}

std::string Microsystem::getHealthStatus() const {
    return "healthy";
}

std::string Microsystem::getServiceInfo() const {
    std::ostringstream oss;
    oss << "{"
        << "\"service\":\"" << m_serviceName << "\","
        << "\"version\":\"" << m_version << "\","
        << "\"uptime\":" << getUptimeSeconds() << ","
        << "\"status\":\"" << getHealthStatus() << "\""
        << "}";
    return oss.str();
}

// end of file Microsystem.cpp
