/**
 * @file      Microsystem.hpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     Microsystem component for microservice functionality
 * @version   0.1
 * @date      22-06-2025
 * @copyright 2025, your company / association / school
 */

#ifndef MICROSYSTEM_HPP_
#define MICROSYSTEM_HPP_

#include <string>
#include <memory>
#include <chrono>

/**
 * @brief Microsystem class providing microservice utilities and functionality
 */
class Microsystem {
private:
    std::string m_serviceName;
    std::string m_version;
    std::chrono::steady_clock::time_point m_startTime;

public:
    /**
     * @brief Constructor
     * @param serviceName Name of the microservice
     * @param version Version of the microservice
     */
    Microsystem(const std::string& serviceName = "MiWebServer", const std::string& version = "0.1.0");

    /**
     * @brief Destructor
     */
    ~Microsystem();

    /**
     * @brief Initialize the microsystem
     * @return true if initialization successful, false otherwise
     */
    bool initialize();

    /**
     * @brief Shutdown the microsystem
     */
    void shutdown();

    /**
     * @brief Get service name
     * @return Service name
     */
    const std::string& getServiceName() const;

    /**
     * @brief Get service version
     * @return Service version
     */
    const std::string& getVersion() const;

    /**
     * @brief Get uptime in seconds
     * @return Uptime in seconds
     */
    double getUptimeSeconds() const;

    /**
     * @brief Get system health status
     * @return Health status as string
     */
    std::string getHealthStatus() const;

    /**
     * @brief Get service information as JSON-like string
     * @return Service info string
     */
    std::string getServiceInfo() const;
};

#endif // MICROSYSTEM_HPP_

