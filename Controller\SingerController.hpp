
#ifndef SINGER_CONTROLLER_HPP_
#define SINGER_CONTROLLER_HPP_

#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/protocol/http/Http.hpp"

#include OATPP_CODEGEN_BEGIN(ApiController)

class SingerController : public oatpp::web::server::api::ApiController {
public:

  SingerController(const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper)
    : oatpp::web::server::api::ApiController(objectMapper,"/singer") {}

  ENDPOINT("GET", "/last_created/", getByLastCreated,QUERY(String,createdDateTime,"createdDateTime"),QUERY(UInt32,itemPerPage,"itemPerPage")) 
{
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                  createdDateTime + "----" + std::to_string(itemPerPage)); 
    response->putHeader("Server", "MiWebServer/1.0.0");
    return response;
  }

  ENDPOINT("GET", "/last_updated/", getByLastUpdated) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200, 
                                  "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Server", "MiWebServer/1.0.0");
    response->putHeader("Content-Type", "application/json");
    return response;
  }

};

#include OATPP_CODEGEN_END(ApiController)

#endif // SINGER_CONTROLLER_HPP_
