/**
 * @file      UserDto.hpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     User Data Transfer Object
 * @version   1.0.0
 * @date      24-06-2025
 * @copyright 2025, your company / association / school
 */

#ifndef USER_DTO_HPP_
#define USER_DTO_HPP_

#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/core/Types.hpp"

#include OATPP_CODEGEN_BEGIN(DTO)

/**
 * @brief User Data Transfer Object
 */
class UserDto : public oatpp::DTO {

  DTO_INIT(UserDto, DTO)

  DTO_FIELD(Int32, id, "id");
  DTO_FIELD(String, name, "name");
  DTO_FIELD(String, email, "email");
  DTO_FIELD(String, created_at, "created_at");

};

/**
 * @brief User creation request DTO
 */
class CreateUserDto : public oatpp::DTO {

  DTO_INIT(CreateUserDto, DTO)

  DTO_FIELD(String, name, "name");
  DTO_FIELD(String, email, "email");

};

/**
 * @brief User update request DTO
 */
class UpdateUserDto : public oatpp::DTO {

  DTO_INIT(UpdateUserDto, DTO)

  DTO_FIELD(String, name, "name");
  DTO_FIELD(String, email, "email");

};

/**
 * @brief API Response DTO
 */
class ApiResponseDto : public oatpp::DTO {

  DTO_INIT(ApiResponseDto, DTO)

  DTO_FIELD(Boolean, success, "success");
  DTO_FIELD(String, message, "message");
  DTO_FIELD(Any, data, "data");

};

#include OATPP_CODEGEN_END(DTO)

#endif // USER_DTO_HPP_
