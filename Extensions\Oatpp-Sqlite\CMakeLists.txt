cmake_minimum_required(VERSION 3.10)

# Create sqlite library from amalgamation
add_library(sqlite STATIC
    sqlite/sqlite3.c
    sqlite/sqlite3.h
)

# Set sqlite include directories
target_include_directories(sqlite
    PUBLIC 
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/sqlite>
        $<INSTALL_INTERFACE:include/sqlite>
)

# Link required libraries for sqlite
if(CMAKE_SYSTEM_NAME MATCHES Linux)
    find_package(Threads REQUIRED)
    target_link_libraries(sqlite INTERFACE Threads::Threads ${CMAKE_DL_LIBS})
endif()

# Create oatpp-sqlite library
add_library(oatpp-sqlite STATIC
    oatpp-sqlite/Connection.cpp
    oatpp-sqlite/Connection.hpp
    oatpp-sqlite/ConnectionProvider.cpp
    oatpp-sqlite/ConnectionProvider.hpp
    oatpp-sqlite/Executor.cpp
    oatpp-sqlite/Executor.hpp
    oatpp-sqlite/QueryResult.cpp
    oatpp-sqlite/QueryResult.hpp
    oatpp-sqlite/Types.hpp
    oatpp-sqlite/Utils.cpp
    oatpp-sqlite/Utils.hpp
    oatpp-sqlite/orm.hpp
)

# Check if mapping directory exists and add files if present
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/mapping")
    file(GLOB_RECURSE MAPPING_SOURCES 
        "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/mapping/*.cpp"
        "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/mapping/*.hpp"
    )
    target_sources(oatpp-sqlite PRIVATE ${MAPPING_SOURCES})
endif()

# Check if ql_template directory exists and add files if present
if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/ql_template")
    file(GLOB_RECURSE QL_TEMPLATE_SOURCES 
        "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/ql_template/*.cpp"
        "${CMAKE_CURRENT_SOURCE_DIR}/oatpp-sqlite/ql_template/*.hpp"
    )
    target_sources(oatpp-sqlite PRIVATE ${QL_TEMPLATE_SOURCES})
endif()

# Set C++ standard for oatpp-sqlite
set_target_properties(oatpp-sqlite PROPERTIES
    CXX_STANDARD 17
    CXX_EXTENSIONS OFF
    CXX_STANDARD_REQUIRED ON
)

# Set include directories for oatpp-sqlite
target_include_directories(oatpp-sqlite
    PUBLIC 
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/sqlite>
        $<INSTALL_INTERFACE:include>
)

# Link oatpp-sqlite with required libraries
target_link_libraries(oatpp-sqlite
    PUBLIC 
        oatpp
        sqlite
)

# Make sqlite3.h globally available
add_library(sqlite3-headers INTERFACE)
target_include_directories(sqlite3-headers INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/sqlite>
    $<INSTALL_INTERFACE:include/sqlite>
)
