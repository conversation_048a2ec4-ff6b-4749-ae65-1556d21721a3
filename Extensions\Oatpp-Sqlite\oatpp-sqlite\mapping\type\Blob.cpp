/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#include "Blob.hpp"
#include "oatpp/encoding/Hex.hpp"
#include "oatpp/core/data/stream/BufferStream.hpp"

namespace oatpp { namespace sqlite { namespace mapping { namespace type {

namespace __class {

  const oatpp::ClassId Blob::CLASS_ID("oatpp::sqlite::Blob");

  oatpp::Type* Blob::createType() {
    oatpp::Type::Info info;
    info.interpretationMap = {{"sqlite", new Inter()}};
    return new oatpp::Type(CLASS_ID, info);
  }

  oatpp::Type* Blob::getType() {
    static Type* type = createType();
    return type;
  }

}

}}}}
