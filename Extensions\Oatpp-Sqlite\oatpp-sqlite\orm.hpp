/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

/**[info]
 *
 * This is just a header file which includes all oatpp-sqlite components:
 *
 * ```cpp
 * #include "Executor.hpp"
 * #include "Types.hpp"
 * #include "Utils.hpp"
 *
 * #include "oatpp/orm/SchemaMigration.hpp"
 * #include "oatpp/orm/DbClient.hpp"
 * #include "oatpp/macro/codegen.hpp"
 * ```
 */

#ifndef oatpp_sqlite_orm_hpp
#define oatpp_sqlite_orm_hpp

#include "Executor.hpp"
#include "Types.hpp"
#include "Utils.hpp"

#include "oatpp/orm/SchemaMigration.hpp"
#include "oatpp/orm/DbClient.hpp"
#include "oatpp/core/macro/codegen.hpp"



#endif // oatpp_sqlite_orm_hpp
