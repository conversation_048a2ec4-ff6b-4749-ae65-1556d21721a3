/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *                         Bened<PERSON><PERSON>-<PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/


#undef DTO_INIT

// Fields

#undef OATPP_MACRO_DTO_FIELD_1
#undef OATPP_MACRO_DTO_FIELD_2
#undef DTO_FIELD

// Fields Info

#undef DTO_FIELD_INFO

// Type Selector

#undef DTO_FIELD_TYPE_SELECTOR

// Hashcode & Equals

#undef OATPP_MACRO_DTO_HC_EQ_PARAM_HC
#undef OATPP_MACRO_DTO_HC_EQ_PARAM_EQ
#undef DTO_HASHCODE_AND_EQUALS
#undef DTO_HC_EQ
