/***************************************************************************
 *
 * Project         _____    __   ____   _      _
 *                (  _  )  /__\ (_  _)_| |_  _| |_
 *                 )(_)(  /(__)\  )( (_   _)(_   _)
 *                (_____)(__)(__)(__)  |_|    |_|
 *
 *
 * Copyright 2018-present, <PERSON><PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 ***************************************************************************/

#include "ParsingError.hpp"

namespace oatpp { namespace parser {

ParsingError::ParsingError(const oatpp::String &message, v_int64 code, v_buff_size position)
  :std::runtime_error(*message)
  , m_message(message)
  , m_code(code)
  , m_position(position)
{}

oatpp::String ParsingError::getMessage() const {
  return m_message;
}

v_int64 ParsingError::getCode() const {
  return m_code;
}

v_buff_size ParsingError::getPosition() const {
  return m_position;
}

}}
