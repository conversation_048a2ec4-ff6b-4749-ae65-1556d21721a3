/**
 * @file      DatabaseService.hpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     Database Service for MiWebServer using oatpp-sqlite
 * @version   1.0.0
 * @date      24-06-2025
 * @copyright 2025, your company / association / school
 */

#ifndef DATABASE_SERVICE_HPP_
#define DATABASE_SERVICE_HPP_

#include "oatpp-sqlite/orm.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/Types.hpp"

#include OATPP_CODEGEN_BEGIN(DbClient)

/**
 * @brief Database client for SQLite operations
 */
class DatabaseClient : public oatpp::orm::DbClient {
public:

  DatabaseClient(const std::shared_ptr<oatpp::orm::Executor>& executor)
    : oatpp::orm::DbClient(executor)
  {
    // Initialize database schema
    executeQuery("CREATE TABLE IF NOT EXISTS users ("
                "id INTEGER PRIMARY KEY AUTOINCREMENT,"
                "name TEXT NOT NULL,"
                "email TEXT UNIQUE NOT NULL,"
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP"
                ")", {});
  }

  QUERY(createUser,
        "INSERT INTO users (name, email) VALUES (:name, :email);",
        PARAM(oatpp::String, name),
        PARAM(oatpp::String, email))

  QUERY(getUserById,
        "SELECT * FROM users WHERE id = :id;",
        PARAM(oatpp::Int32, id))

  QUERY(getAllUsers,
        "SELECT * FROM users ORDER BY created_at DESC;")

  QUERY(updateUser,
        "UPDATE users SET name = :name, email = :email WHERE id = :id;",
        PARAM(oatpp::Int32, id),
        PARAM(oatpp::String, name),
        PARAM(oatpp::String, email))

  QUERY(deleteUser,
        "DELETE FROM users WHERE id = :id;",
        PARAM(oatpp::Int32, id))

  QUERY(getUserCount,
        "SELECT COUNT(*) as count FROM users;")

};

#include OATPP_CODEGEN_END(DbClient)

/**
 * @brief Database Service class for managing database operations
 */
class DatabaseService {
private:
  std::shared_ptr<DatabaseClient> m_databaseClient;

public:
  /**
   * @brief Constructor
   * @param databaseClient Database client instance
   */
  DatabaseService(const std::shared_ptr<DatabaseClient>& databaseClient)
    : m_databaseClient(databaseClient) {}

  /**
   * @brief Create a new user
   * @param name User name
   * @param email User email
   * @return Success status
   */
  bool createUser(const oatpp::String& name, const oatpp::String& email);

  /**
   * @brief Get user by ID
   * @param id User ID
   * @return User data as JSON string
   */
  oatpp::String getUserById(oatpp::Int32 id);

  /**
   * @brief Get all users
   * @return All users as JSON string
   */
  oatpp::String getAllUsers();

  /**
   * @brief Update user information
   * @param id User ID
   * @param name New name
   * @param email New email
   * @return Success status
   */
  bool updateUser(oatpp::Int32 id, const oatpp::String& name, const oatpp::String& email);

  /**
   * @brief Delete user by ID
   * @param id User ID
   * @return Success status
   */
  bool deleteUser(oatpp::Int32 id);

  /**
   * @brief Get total user count
   * @return User count
   */
  oatpp::Int32 getUserCount();

  /**
   * @brief Get database health status
   * @return Health status as JSON string
   */
  oatpp::String getHealthStatus();
};

#endif // DATABASE_SERVICE_HPP_
