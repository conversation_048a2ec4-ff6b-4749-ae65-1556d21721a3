# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/MiWebServer.dir/all
all: Oat++/all
all: Extensions/Oatpp-Sqlite/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/MiWebServer.dir/codegen
codegen: Oat++/codegen
codegen: Extensions/Oatpp-Sqlite/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: Oat++/preinstall
preinstall: Extensions/Oatpp-Sqlite/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/MiWebServer.dir/clean
clean: Oat++/clean
clean: Extensions/Oatpp-Sqlite/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory Extensions/Oatpp-Sqlite

# Recursive "all" directory target.
Extensions/Oatpp-Sqlite/all: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all
Extensions/Oatpp-Sqlite/all: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all
.PHONY : Extensions/Oatpp-Sqlite/all

# Recursive "codegen" directory target.
Extensions/Oatpp-Sqlite/codegen: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen
Extensions/Oatpp-Sqlite/codegen: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen
.PHONY : Extensions/Oatpp-Sqlite/codegen

# Recursive "preinstall" directory target.
Extensions/Oatpp-Sqlite/preinstall:
.PHONY : Extensions/Oatpp-Sqlite/preinstall

# Recursive "clean" directory target.
Extensions/Oatpp-Sqlite/clean: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean
Extensions/Oatpp-Sqlite/clean: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean
.PHONY : Extensions/Oatpp-Sqlite/clean

#=============================================================================
# Directory level rules for directory Oat++

# Recursive "all" directory target.
Oat++/all: Oat++/CMakeFiles/oatpp.dir/all
.PHONY : Oat++/all

# Recursive "codegen" directory target.
Oat++/codegen: Oat++/CMakeFiles/oatpp.dir/codegen
.PHONY : Oat++/codegen

# Recursive "preinstall" directory target.
Oat++/preinstall:
.PHONY : Oat++/preinstall

# Recursive "clean" directory target.
Oat++/clean: Oat++/CMakeFiles/oatpp.dir/clean
.PHONY : Oat++/clean

#=============================================================================
# Target rules for target CMakeFiles/MiWebServer.dir

# All Build rule for target.
CMakeFiles/MiWebServer.dir/all: Oat++/CMakeFiles/oatpp.dir/all
CMakeFiles/MiWebServer.dir/all: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all
CMakeFiles/MiWebServer.dir/all: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=1,2 "Built target MiWebServer"
.PHONY : CMakeFiles/MiWebServer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MiWebServer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 100
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/MiWebServer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : CMakeFiles/MiWebServer.dir/rule

# Convenience name for target.
MiWebServer: CMakeFiles/MiWebServer.dir/rule
.PHONY : MiWebServer

# codegen rule for target.
CMakeFiles/MiWebServer.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=1,2 "Finished codegen for target MiWebServer"
.PHONY : CMakeFiles/MiWebServer.dir/codegen

# clean rule for target.
CMakeFiles/MiWebServer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/clean
.PHONY : CMakeFiles/MiWebServer.dir/clean

#=============================================================================
# Target rules for target Oat++/CMakeFiles/oatpp.dir

# All Build rule for target.
Oat++/CMakeFiles/oatpp.dir/all:
	$(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/depend
	$(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90 "Built target oatpp"
.PHONY : Oat++/CMakeFiles/oatpp.dir/all

# Build rule for subdir invocation for target.
Oat++/CMakeFiles/oatpp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 88
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/CMakeFiles/oatpp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : Oat++/CMakeFiles/oatpp.dir/rule

# Convenience name for target.
oatpp: Oat++/CMakeFiles/oatpp.dir/rule
.PHONY : oatpp

# codegen rule for target.
Oat++/CMakeFiles/oatpp.dir/codegen:
	$(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90 "Finished codegen for target oatpp"
.PHONY : Oat++/CMakeFiles/oatpp.dir/codegen

# clean rule for target.
Oat++/CMakeFiles/oatpp.dir/clean:
	$(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/clean
.PHONY : Oat++/CMakeFiles/oatpp.dir/clean

#=============================================================================
# Target rules for target Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir

# All Build rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/depend
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=99,100 "Built target sqlite"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all

# Build rule for subdir invocation for target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule

# Convenience name for target.
sqlite: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule
.PHONY : sqlite

# codegen rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=99,100 "Finished codegen for target sqlite"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen

# clean rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean

#=============================================================================
# Target rules for target Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir

# All Build rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all: Oat++/CMakeFiles/oatpp.dir/all
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/all
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/depend
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=91,92,93,94,95,96,97,98 "Built target oatpp-sqlite"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all

# Build rule for subdir invocation for target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 98
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule

# Convenience name for target.
oatpp-sqlite: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule
.PHONY : oatpp-sqlite

# codegen rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=91,92,93,94,95,96,97,98 "Finished codegen for target oatpp-sqlite"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen

# clean rule for target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

