# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

# Include any dependencies generated for this target.
include CMakeFiles/MiWebServer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/MiWebServer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/MiWebServer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MiWebServer.dir/flags.make

CMakeFiles/MiWebServer.dir/codegen:
.PHONY : CMakeFiles/MiWebServer.dir/codegen

CMakeFiles/MiWebServer.dir/main.cpp.obj: CMakeFiles/MiWebServer.dir/flags.make
CMakeFiles/MiWebServer.dir/main.cpp.obj: CMakeFiles/MiWebServer.dir/includes_CXX.rsp
CMakeFiles/MiWebServer.dir/main.cpp.obj: D:/MiWebApp/WebServerApp/main.cpp
CMakeFiles/MiWebServer.dir/main.cpp.obj: CMakeFiles/MiWebServer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MiWebServer.dir/main.cpp.obj"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MiWebServer.dir/main.cpp.obj -MF CMakeFiles\MiWebServer.dir\main.cpp.obj.d -o CMakeFiles\MiWebServer.dir\main.cpp.obj -c D:\MiWebApp\WebServerApp\main.cpp

CMakeFiles/MiWebServer.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MiWebServer.dir/main.cpp.i"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\main.cpp > CMakeFiles\MiWebServer.dir\main.cpp.i

CMakeFiles/MiWebServer.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MiWebServer.dir/main.cpp.s"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\main.cpp -o CMakeFiles\MiWebServer.dir\main.cpp.s

CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj: CMakeFiles/MiWebServer.dir/flags.make
CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj: CMakeFiles/MiWebServer.dir/includes_CXX.rsp
CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj: D:/MiWebApp/WebServerApp/Service/DatabaseService.cpp
CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj: CMakeFiles/MiWebServer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj -MF CMakeFiles\MiWebServer.dir\Service\DatabaseService.cpp.obj.d -o CMakeFiles\MiWebServer.dir\Service\DatabaseService.cpp.obj -c D:\MiWebApp\WebServerApp\Service\DatabaseService.cpp

CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.i"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Service\DatabaseService.cpp > CMakeFiles\MiWebServer.dir\Service\DatabaseService.cpp.i

CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.s"
	C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Service\DatabaseService.cpp -o CMakeFiles\MiWebServer.dir\Service\DatabaseService.cpp.s

# Object files for target MiWebServer
MiWebServer_OBJECTS = \
"CMakeFiles/MiWebServer.dir/main.cpp.obj" \
"CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj"

# External object files for target MiWebServer
MiWebServer_EXTERNAL_OBJECTS =

MiWebServer.exe: CMakeFiles/MiWebServer.dir/main.cpp.obj
MiWebServer.exe: CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj
MiWebServer.exe: CMakeFiles/MiWebServer.dir/build.make
MiWebServer.exe: Oat++/liboatpp.a
MiWebServer.exe: Extensions/Oatpp-Sqlite/liboatpp-sqlite.a
MiWebServer.exe: Oat++/liboatpp.a
MiWebServer.exe: Extensions/Oatpp-Sqlite/libsqlite.a
MiWebServer.exe: CMakeFiles/MiWebServer.dir/linkLibs.rsp
MiWebServer.exe: CMakeFiles/MiWebServer.dir/objects1.rsp
MiWebServer.exe: CMakeFiles/MiWebServer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable MiWebServer.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\MiWebServer.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/MiWebServer.dir/build: MiWebServer.exe
.PHONY : CMakeFiles/MiWebServer.dir/build

CMakeFiles/MiWebServer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\MiWebServer.dir\cmake_clean.cmake
.PHONY : CMakeFiles/MiWebServer.dir/clean

CMakeFiles/MiWebServer.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\MiWebApp\WebServerApp D:\MiWebApp\WebServerApp D:\MiWebApp\WebServerApp\build D:\MiWebApp\WebServerApp\build D:\MiWebApp\WebServerApp\build\CMakeFiles\MiWebServer.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/MiWebServer.dir/depend

