"C:\Program Files\CMake\bin\cmake.exe" -E rm -f CMakeFiles\MiWebServer.dir/objects.a
C:\mingw810_64\bin\ar.exe qc CMakeFiles\MiWebServer.dir/objects.a @CMakeFiles\MiWebServer.dir\objects1.rsp
C:\mingw810_64\bin\c++.exe -Wl,--whole-archive CMakeFiles\MiWebServer.dir/objects.a -Wl,--no-whole-archive -o MiWebServer.exe -Wl,--out-implib,libMiWebServer.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\MiWebServer.dir\linkLibs.rsp
