
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Connection.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ConnectionProvider.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Executor.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/QueryResult.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Utils.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Deserializer.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/ResultMapper.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Serializer.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/type/Blob.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/Parser.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj.d"
  "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/TemplateValueProvider.cpp" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj" "gcc" "Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
