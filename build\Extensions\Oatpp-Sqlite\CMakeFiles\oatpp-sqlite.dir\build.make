# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

# Include any dependencies generated for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.make

# Include the progress variables for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/progress.make

# Include the compile flags for this target's objects.
include Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen:
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/codegen

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Connection.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Connection.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Connection.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Connection.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Connection.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Connection.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Connection.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Connection.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ConnectionProvider.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ConnectionProvider.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ConnectionProvider.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ConnectionProvider.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ConnectionProvider.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ConnectionProvider.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Executor.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Executor.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Executor.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Executor.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Executor.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Executor.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Executor.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Executor.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/QueryResult.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\QueryResult.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\QueryResult.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\QueryResult.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\QueryResult.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\QueryResult.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\QueryResult.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\QueryResult.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/Utils.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Utils.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Utils.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Utils.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Utils.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Utils.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\Utils.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\Utils.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Deserializer.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Deserializer.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Deserializer.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Deserializer.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Deserializer.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Deserializer.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Deserializer.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Deserializer.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/ResultMapper.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\ResultMapper.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\ResultMapper.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\ResultMapper.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\ResultMapper.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\ResultMapper.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\ResultMapper.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\ResultMapper.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/Serializer.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Serializer.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Serializer.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Serializer.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Serializer.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Serializer.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\Serializer.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\Serializer.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/mapping/type/Blob.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\type\Blob.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\type\Blob.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\type\Blob.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\type\Blob.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\type\Blob.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\mapping\type\Blob.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\mapping\type\Blob.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/Parser.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\Parser.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\Parser.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\Parser.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\Parser.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\Parser.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\Parser.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\Parser.cpp.s

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/includes_CXX.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/oatpp-sqlite/ql_template/TemplateValueProvider.cpp
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj -MF CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\TemplateValueProvider.cpp.obj.d -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\TemplateValueProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\TemplateValueProvider.cpp

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\TemplateValueProvider.cpp > CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\TemplateValueProvider.cpp.i

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\oatpp-sqlite\ql_template\TemplateValueProvider.cpp -o CMakeFiles\oatpp-sqlite.dir\oatpp-sqlite\ql_template\TemplateValueProvider.cpp.s

# Object files for target oatpp-sqlite
oatpp__sqlite_OBJECTS = \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj" \
"CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj"

# External object files for target oatpp-sqlite
oatpp__sqlite_EXTERNAL_OBJECTS =

Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build.make
Extensions/Oatpp-Sqlite/liboatpp-sqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX static library liboatpp-sqlite.a"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -P CMakeFiles\oatpp-sqlite.dir\cmake_clean_target.cmake
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\oatpp-sqlite.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build: Extensions/Oatpp-Sqlite/liboatpp-sqlite.a
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean:
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -P CMakeFiles\oatpp-sqlite.dir\cmake_clean.cmake
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/clean

Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\MiWebApp\WebServerApp D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite D:\MiWebApp\WebServerApp\build D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/depend

