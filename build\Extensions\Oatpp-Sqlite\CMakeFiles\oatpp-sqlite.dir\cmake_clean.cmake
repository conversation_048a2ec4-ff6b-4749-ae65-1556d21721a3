file(REMOVE_RECURSE
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj.d"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj"
  "CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj.d"
  "liboatpp-sqlite.a"
  "liboatpp-sqlite.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/oatpp-sqlite.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
