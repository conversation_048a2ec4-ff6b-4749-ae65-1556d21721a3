# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

# Include any dependencies generated for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/compiler_depend.make

# Include the progress variables for this target.
include Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/progress.make

# Include the compile flags for this target's objects.
include Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/flags.make

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen:
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/codegen

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/flags.make
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/includes_C.rsp
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj: D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/sqlite/sqlite3.c
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj -MF CMakeFiles\sqlite.dir\sqlite\sqlite3.c.obj.d -o CMakeFiles\sqlite.dir\sqlite\sqlite3.c.obj -c D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\sqlite\sqlite3.c

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sqlite.dir/sqlite/sqlite3.c.i"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\sqlite\sqlite3.c > CMakeFiles\sqlite.dir\sqlite\sqlite3.c.i

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sqlite.dir/sqlite/sqlite3.c.s"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && C:\mingw810_64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite\sqlite\sqlite3.c -o CMakeFiles\sqlite.dir\sqlite\sqlite3.c.s

# Object files for target sqlite
sqlite_OBJECTS = \
"CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj"

# External object files for target sqlite
sqlite_EXTERNAL_OBJECTS =

Extensions/Oatpp-Sqlite/libsqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj
Extensions/Oatpp-Sqlite/libsqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build.make
Extensions/Oatpp-Sqlite/libsqlite.a: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C static library libsqlite.a"
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -P CMakeFiles\sqlite.dir\cmake_clean_target.cmake
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\sqlite.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build: Extensions/Oatpp-Sqlite/libsqlite.a
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean:
	cd /d D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite && $(CMAKE_COMMAND) -P CMakeFiles\sqlite.dir\cmake_clean.cmake
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/clean

Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\MiWebApp\WebServerApp D:\MiWebApp\WebServerApp\Extensions\Oatpp-Sqlite D:\MiWebApp\WebServerApp\build D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/depend

