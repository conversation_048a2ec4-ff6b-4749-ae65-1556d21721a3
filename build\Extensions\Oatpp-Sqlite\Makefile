# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite\\CMakeFiles\progress.marks
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\Extensions\Oatpp-Sqlite\\CMakeFiles\progress.marks
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/codegen
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule

# Convenience name for target.
sqlite: Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/rule
.PHONY : sqlite

# fast build rule for target.
sqlite/fast:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build
.PHONY : sqlite/fast

# Convenience name for target.
Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule
.PHONY : Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule

# Convenience name for target.
oatpp-sqlite: Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/rule
.PHONY : oatpp-sqlite

# fast build rule for target.
oatpp-sqlite/fast:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build
.PHONY : oatpp-sqlite/fast

oatpp-sqlite/Connection.obj: oatpp-sqlite/Connection.cpp.obj
.PHONY : oatpp-sqlite/Connection.obj

# target to build an object file
oatpp-sqlite/Connection.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.obj
.PHONY : oatpp-sqlite/Connection.cpp.obj

oatpp-sqlite/Connection.i: oatpp-sqlite/Connection.cpp.i
.PHONY : oatpp-sqlite/Connection.i

# target to preprocess a source file
oatpp-sqlite/Connection.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.i
.PHONY : oatpp-sqlite/Connection.cpp.i

oatpp-sqlite/Connection.s: oatpp-sqlite/Connection.cpp.s
.PHONY : oatpp-sqlite/Connection.s

# target to generate assembly for a file
oatpp-sqlite/Connection.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Connection.cpp.s
.PHONY : oatpp-sqlite/Connection.cpp.s

oatpp-sqlite/ConnectionProvider.obj: oatpp-sqlite/ConnectionProvider.cpp.obj
.PHONY : oatpp-sqlite/ConnectionProvider.obj

# target to build an object file
oatpp-sqlite/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.obj
.PHONY : oatpp-sqlite/ConnectionProvider.cpp.obj

oatpp-sqlite/ConnectionProvider.i: oatpp-sqlite/ConnectionProvider.cpp.i
.PHONY : oatpp-sqlite/ConnectionProvider.i

# target to preprocess a source file
oatpp-sqlite/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.i
.PHONY : oatpp-sqlite/ConnectionProvider.cpp.i

oatpp-sqlite/ConnectionProvider.s: oatpp-sqlite/ConnectionProvider.cpp.s
.PHONY : oatpp-sqlite/ConnectionProvider.s

# target to generate assembly for a file
oatpp-sqlite/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ConnectionProvider.cpp.s
.PHONY : oatpp-sqlite/ConnectionProvider.cpp.s

oatpp-sqlite/Executor.obj: oatpp-sqlite/Executor.cpp.obj
.PHONY : oatpp-sqlite/Executor.obj

# target to build an object file
oatpp-sqlite/Executor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.obj
.PHONY : oatpp-sqlite/Executor.cpp.obj

oatpp-sqlite/Executor.i: oatpp-sqlite/Executor.cpp.i
.PHONY : oatpp-sqlite/Executor.i

# target to preprocess a source file
oatpp-sqlite/Executor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.i
.PHONY : oatpp-sqlite/Executor.cpp.i

oatpp-sqlite/Executor.s: oatpp-sqlite/Executor.cpp.s
.PHONY : oatpp-sqlite/Executor.s

# target to generate assembly for a file
oatpp-sqlite/Executor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Executor.cpp.s
.PHONY : oatpp-sqlite/Executor.cpp.s

oatpp-sqlite/QueryResult.obj: oatpp-sqlite/QueryResult.cpp.obj
.PHONY : oatpp-sqlite/QueryResult.obj

# target to build an object file
oatpp-sqlite/QueryResult.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.obj
.PHONY : oatpp-sqlite/QueryResult.cpp.obj

oatpp-sqlite/QueryResult.i: oatpp-sqlite/QueryResult.cpp.i
.PHONY : oatpp-sqlite/QueryResult.i

# target to preprocess a source file
oatpp-sqlite/QueryResult.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.i
.PHONY : oatpp-sqlite/QueryResult.cpp.i

oatpp-sqlite/QueryResult.s: oatpp-sqlite/QueryResult.cpp.s
.PHONY : oatpp-sqlite/QueryResult.s

# target to generate assembly for a file
oatpp-sqlite/QueryResult.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/QueryResult.cpp.s
.PHONY : oatpp-sqlite/QueryResult.cpp.s

oatpp-sqlite/Utils.obj: oatpp-sqlite/Utils.cpp.obj
.PHONY : oatpp-sqlite/Utils.obj

# target to build an object file
oatpp-sqlite/Utils.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.obj
.PHONY : oatpp-sqlite/Utils.cpp.obj

oatpp-sqlite/Utils.i: oatpp-sqlite/Utils.cpp.i
.PHONY : oatpp-sqlite/Utils.i

# target to preprocess a source file
oatpp-sqlite/Utils.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.i
.PHONY : oatpp-sqlite/Utils.cpp.i

oatpp-sqlite/Utils.s: oatpp-sqlite/Utils.cpp.s
.PHONY : oatpp-sqlite/Utils.s

# target to generate assembly for a file
oatpp-sqlite/Utils.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/Utils.cpp.s
.PHONY : oatpp-sqlite/Utils.cpp.s

oatpp-sqlite/mapping/Deserializer.obj: oatpp-sqlite/mapping/Deserializer.cpp.obj
.PHONY : oatpp-sqlite/mapping/Deserializer.obj

# target to build an object file
oatpp-sqlite/mapping/Deserializer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.obj
.PHONY : oatpp-sqlite/mapping/Deserializer.cpp.obj

oatpp-sqlite/mapping/Deserializer.i: oatpp-sqlite/mapping/Deserializer.cpp.i
.PHONY : oatpp-sqlite/mapping/Deserializer.i

# target to preprocess a source file
oatpp-sqlite/mapping/Deserializer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.i
.PHONY : oatpp-sqlite/mapping/Deserializer.cpp.i

oatpp-sqlite/mapping/Deserializer.s: oatpp-sqlite/mapping/Deserializer.cpp.s
.PHONY : oatpp-sqlite/mapping/Deserializer.s

# target to generate assembly for a file
oatpp-sqlite/mapping/Deserializer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Deserializer.cpp.s
.PHONY : oatpp-sqlite/mapping/Deserializer.cpp.s

oatpp-sqlite/mapping/ResultMapper.obj: oatpp-sqlite/mapping/ResultMapper.cpp.obj
.PHONY : oatpp-sqlite/mapping/ResultMapper.obj

# target to build an object file
oatpp-sqlite/mapping/ResultMapper.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.obj
.PHONY : oatpp-sqlite/mapping/ResultMapper.cpp.obj

oatpp-sqlite/mapping/ResultMapper.i: oatpp-sqlite/mapping/ResultMapper.cpp.i
.PHONY : oatpp-sqlite/mapping/ResultMapper.i

# target to preprocess a source file
oatpp-sqlite/mapping/ResultMapper.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.i
.PHONY : oatpp-sqlite/mapping/ResultMapper.cpp.i

oatpp-sqlite/mapping/ResultMapper.s: oatpp-sqlite/mapping/ResultMapper.cpp.s
.PHONY : oatpp-sqlite/mapping/ResultMapper.s

# target to generate assembly for a file
oatpp-sqlite/mapping/ResultMapper.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/ResultMapper.cpp.s
.PHONY : oatpp-sqlite/mapping/ResultMapper.cpp.s

oatpp-sqlite/mapping/Serializer.obj: oatpp-sqlite/mapping/Serializer.cpp.obj
.PHONY : oatpp-sqlite/mapping/Serializer.obj

# target to build an object file
oatpp-sqlite/mapping/Serializer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.obj
.PHONY : oatpp-sqlite/mapping/Serializer.cpp.obj

oatpp-sqlite/mapping/Serializer.i: oatpp-sqlite/mapping/Serializer.cpp.i
.PHONY : oatpp-sqlite/mapping/Serializer.i

# target to preprocess a source file
oatpp-sqlite/mapping/Serializer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.i
.PHONY : oatpp-sqlite/mapping/Serializer.cpp.i

oatpp-sqlite/mapping/Serializer.s: oatpp-sqlite/mapping/Serializer.cpp.s
.PHONY : oatpp-sqlite/mapping/Serializer.s

# target to generate assembly for a file
oatpp-sqlite/mapping/Serializer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/Serializer.cpp.s
.PHONY : oatpp-sqlite/mapping/Serializer.cpp.s

oatpp-sqlite/mapping/type/Blob.obj: oatpp-sqlite/mapping/type/Blob.cpp.obj
.PHONY : oatpp-sqlite/mapping/type/Blob.obj

# target to build an object file
oatpp-sqlite/mapping/type/Blob.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.obj
.PHONY : oatpp-sqlite/mapping/type/Blob.cpp.obj

oatpp-sqlite/mapping/type/Blob.i: oatpp-sqlite/mapping/type/Blob.cpp.i
.PHONY : oatpp-sqlite/mapping/type/Blob.i

# target to preprocess a source file
oatpp-sqlite/mapping/type/Blob.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.i
.PHONY : oatpp-sqlite/mapping/type/Blob.cpp.i

oatpp-sqlite/mapping/type/Blob.s: oatpp-sqlite/mapping/type/Blob.cpp.s
.PHONY : oatpp-sqlite/mapping/type/Blob.s

# target to generate assembly for a file
oatpp-sqlite/mapping/type/Blob.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/mapping/type/Blob.cpp.s
.PHONY : oatpp-sqlite/mapping/type/Blob.cpp.s

oatpp-sqlite/ql_template/Parser.obj: oatpp-sqlite/ql_template/Parser.cpp.obj
.PHONY : oatpp-sqlite/ql_template/Parser.obj

# target to build an object file
oatpp-sqlite/ql_template/Parser.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.obj
.PHONY : oatpp-sqlite/ql_template/Parser.cpp.obj

oatpp-sqlite/ql_template/Parser.i: oatpp-sqlite/ql_template/Parser.cpp.i
.PHONY : oatpp-sqlite/ql_template/Parser.i

# target to preprocess a source file
oatpp-sqlite/ql_template/Parser.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.i
.PHONY : oatpp-sqlite/ql_template/Parser.cpp.i

oatpp-sqlite/ql_template/Parser.s: oatpp-sqlite/ql_template/Parser.cpp.s
.PHONY : oatpp-sqlite/ql_template/Parser.s

# target to generate assembly for a file
oatpp-sqlite/ql_template/Parser.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/Parser.cpp.s
.PHONY : oatpp-sqlite/ql_template/Parser.cpp.s

oatpp-sqlite/ql_template/TemplateValueProvider.obj: oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.obj

# target to build an object file
oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.cpp.obj

oatpp-sqlite/ql_template/TemplateValueProvider.i: oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.i

# target to preprocess a source file
oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.cpp.i

oatpp-sqlite/ql_template/TemplateValueProvider.s: oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.s

# target to generate assembly for a file
oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s
.PHONY : oatpp-sqlite/ql_template/TemplateValueProvider.cpp.s

sqlite/sqlite3.obj: sqlite/sqlite3.c.obj
.PHONY : sqlite/sqlite3.obj

# target to build an object file
sqlite/sqlite3.c.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.obj
.PHONY : sqlite/sqlite3.c.obj

sqlite/sqlite3.i: sqlite/sqlite3.c.i
.PHONY : sqlite/sqlite3.i

# target to preprocess a source file
sqlite/sqlite3.c.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.i
.PHONY : sqlite/sqlite3.c.i

sqlite/sqlite3.s: sqlite/sqlite3.c.s
.PHONY : sqlite/sqlite3.s

# target to generate assembly for a file
sqlite/sqlite3.c.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/sqlite/sqlite3.c.s
.PHONY : sqlite/sqlite3.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... codegen
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... oatpp-sqlite
	@echo ... sqlite
	@echo ... oatpp-sqlite/Connection.obj
	@echo ... oatpp-sqlite/Connection.i
	@echo ... oatpp-sqlite/Connection.s
	@echo ... oatpp-sqlite/ConnectionProvider.obj
	@echo ... oatpp-sqlite/ConnectionProvider.i
	@echo ... oatpp-sqlite/ConnectionProvider.s
	@echo ... oatpp-sqlite/Executor.obj
	@echo ... oatpp-sqlite/Executor.i
	@echo ... oatpp-sqlite/Executor.s
	@echo ... oatpp-sqlite/QueryResult.obj
	@echo ... oatpp-sqlite/QueryResult.i
	@echo ... oatpp-sqlite/QueryResult.s
	@echo ... oatpp-sqlite/Utils.obj
	@echo ... oatpp-sqlite/Utils.i
	@echo ... oatpp-sqlite/Utils.s
	@echo ... oatpp-sqlite/mapping/Deserializer.obj
	@echo ... oatpp-sqlite/mapping/Deserializer.i
	@echo ... oatpp-sqlite/mapping/Deserializer.s
	@echo ... oatpp-sqlite/mapping/ResultMapper.obj
	@echo ... oatpp-sqlite/mapping/ResultMapper.i
	@echo ... oatpp-sqlite/mapping/ResultMapper.s
	@echo ... oatpp-sqlite/mapping/Serializer.obj
	@echo ... oatpp-sqlite/mapping/Serializer.i
	@echo ... oatpp-sqlite/mapping/Serializer.s
	@echo ... oatpp-sqlite/mapping/type/Blob.obj
	@echo ... oatpp-sqlite/mapping/type/Blob.i
	@echo ... oatpp-sqlite/mapping/type/Blob.s
	@echo ... oatpp-sqlite/ql_template/Parser.obj
	@echo ... oatpp-sqlite/ql_template/Parser.i
	@echo ... oatpp-sqlite/ql_template/Parser.s
	@echo ... oatpp-sqlite/ql_template/TemplateValueProvider.obj
	@echo ... oatpp-sqlite/ql_template/TemplateValueProvider.i
	@echo ... oatpp-sqlite/ql_template/TemplateValueProvider.s
	@echo ... sqlite/sqlite3.obj
	@echo ... sqlite/sqlite3.i
	@echo ... sqlite/sqlite3.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

