# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 codegen
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named MiWebServer

# Build rule for target.
MiWebServer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 MiWebServer
.PHONY : MiWebServer

# fast build rule for target.
MiWebServer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/build
.PHONY : MiWebServer/fast

#=============================================================================
# Target rules for targets named oatpp

# Build rule for target.
oatpp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 oatpp
.PHONY : oatpp

# fast build rule for target.
oatpp/fast:
	$(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/build
.PHONY : oatpp/fast

#=============================================================================
# Target rules for targets named sqlite

# Build rule for target.
sqlite: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 sqlite
.PHONY : sqlite

# fast build rule for target.
sqlite/fast:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/sqlite.dir/build
.PHONY : sqlite/fast

#=============================================================================
# Target rules for targets named oatpp-sqlite

# Build rule for target.
oatpp-sqlite: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 oatpp-sqlite
.PHONY : oatpp-sqlite

# fast build rule for target.
oatpp-sqlite/fast:
	$(MAKE) $(MAKESILENT) -f Extensions\Oatpp-Sqlite\CMakeFiles\oatpp-sqlite.dir\build.make Extensions/Oatpp-Sqlite/CMakeFiles/oatpp-sqlite.dir/build
.PHONY : oatpp-sqlite/fast

Service/DatabaseService.obj: Service/DatabaseService.cpp.obj
.PHONY : Service/DatabaseService.obj

# target to build an object file
Service/DatabaseService.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.obj
.PHONY : Service/DatabaseService.cpp.obj

Service/DatabaseService.i: Service/DatabaseService.cpp.i
.PHONY : Service/DatabaseService.i

# target to preprocess a source file
Service/DatabaseService.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.i
.PHONY : Service/DatabaseService.cpp.i

Service/DatabaseService.s: Service/DatabaseService.cpp.s
.PHONY : Service/DatabaseService.s

# target to generate assembly for a file
Service/DatabaseService.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/Service/DatabaseService.cpp.s
.PHONY : Service/DatabaseService.cpp.s

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\MiWebServer.dir\build.make CMakeFiles/MiWebServer.dir/main.cpp.s
.PHONY : main.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... codegen
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... MiWebServer
	@echo ... oatpp
	@echo ... oatpp-sqlite
	@echo ... sqlite
	@echo ... Service/DatabaseService.obj
	@echo ... Service/DatabaseService.i
	@echo ... Service/DatabaseService.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

