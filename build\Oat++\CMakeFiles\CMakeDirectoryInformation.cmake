# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Relative path conversion top directories.
set(CMAKE_RELATIVE_PATH_TOP_SOURCE "D:/MiWebApp/WebServerApp")
set(CMAKE_RELATIVE_PATH_TOP_BINARY "D:/MiWebApp/WebServerApp/build")

# Force unix paths in dependencies.
set(CMAKE_FORCE_UNIX_PATHS 1)


# The C and CXX include file regular expressions for this directory.
set(CMAKE_C_INCLUDE_REGEX_SCAN "^.*$")
set(CMAKE_C_INCLUDE_REGEX_COMPLAIN "^$")
set(CMAKE_CXX_INCLUDE_REGEX_SCAN ${CMAKE_C_INCLUDE_REGEX_SCAN})
set(CMAKE_CXX_INCLUDE_REGEX_COMPLAIN ${CMAKE_C_INCLUDE_REGEX_COMPLAIN})
