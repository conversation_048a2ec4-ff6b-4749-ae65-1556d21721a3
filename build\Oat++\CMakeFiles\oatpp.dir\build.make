# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

# Include any dependencies generated for this target.
include Oat++/CMakeFiles/oatpp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include Oat++/CMakeFiles/oatpp.dir/compiler_depend.make

# Include the progress variables for this target.
include Oat++/CMakeFiles/oatpp.dir/progress.make

# Include the compile flags for this target's objects.
include Oat++/CMakeFiles/oatpp.dir/flags.make

Oat++/CMakeFiles/oatpp.dir/codegen:
.PHONY : Oat++/CMakeFiles/oatpp.dir/codegen

Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/algorithm/CRC.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\algorithm\CRC.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\algorithm\CRC.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\algorithm\CRC.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\algorithm\CRC.cpp > CMakeFiles\oatpp.dir\oatpp\algorithm\CRC.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\algorithm\CRC.cpp -o CMakeFiles\oatpp.dir\oatpp\algorithm\CRC.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/IODefinitions.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\IODefinitions.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\IODefinitions.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\IODefinitions.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\IODefinitions.cpp > CMakeFiles\oatpp.dir\oatpp\core\IODefinitions.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\IODefinitions.cpp -o CMakeFiles\oatpp.dir\oatpp\core\IODefinitions.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/ConditionVariable.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\ConditionVariable.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\ConditionVariable.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\ConditionVariable.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\ConditionVariable.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\ConditionVariable.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\ConditionVariable.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\ConditionVariable.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Coroutine.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\Coroutine.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\Coroutine.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Coroutine.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Coroutine.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\Coroutine.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Coroutine.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\Coroutine.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/CoroutineWaitList.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\CoroutineWaitList.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\CoroutineWaitList.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\CoroutineWaitList.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\CoroutineWaitList.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\CoroutineWaitList.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\CoroutineWaitList.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\CoroutineWaitList.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Error.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\Error.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\Error.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Error.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Error.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\Error.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Error.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\Error.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Executor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\Executor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\Executor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Executor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Executor.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\Executor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Executor.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\Executor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Lock.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\Lock.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\Lock.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Lock.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Lock.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\Lock.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Lock.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\Lock.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Processor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\Processor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\Processor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Processor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Processor.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\Processor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\Processor.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\Processor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_common.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_common.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_common.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_common.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_common.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_common.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_common.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_common.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_epoll.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_epoll.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_epoll.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_epoll.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_epoll.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_epoll.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_epoll.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_epoll.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_kqueue.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_kqueue.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_kqueue.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_kqueue.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_kqueue.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_kqueue.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_kqueue.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_kqueue.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOEventWorker_stub.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_stub.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_stub.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_stub.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_stub.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_stub.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOEventWorker_stub.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOEventWorker_stub.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/IOWorker.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOWorker.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOWorker.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOWorker.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOWorker.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOWorker.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\IOWorker.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\IOWorker.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/TimerWorker.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\TimerWorker.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\TimerWorker.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\TimerWorker.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\TimerWorker.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\TimerWorker.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\TimerWorker.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\TimerWorker.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/worker/Worker.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\async\worker\Worker.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\Worker.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\Worker.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\Worker.cpp > CMakeFiles\oatpp.dir\oatpp\core\async\worker\Worker.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\async\worker\Worker.cpp -o CMakeFiles\oatpp.dir\oatpp\core\async\worker\Worker.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/CommandLineArguments.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\base\CommandLineArguments.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\base\CommandLineArguments.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\CommandLineArguments.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\CommandLineArguments.cpp > CMakeFiles\oatpp.dir\oatpp\core\base\CommandLineArguments.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\CommandLineArguments.cpp -o CMakeFiles\oatpp.dir\oatpp\core\base\CommandLineArguments.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Countable.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\base\Countable.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\base\Countable.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Countable.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Countable.cpp > CMakeFiles\oatpp.dir\oatpp\core\base\Countable.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Countable.cpp -o CMakeFiles\oatpp.dir\oatpp\core\base\Countable.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Environment.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\base\Environment.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\base\Environment.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Environment.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Environment.cpp > CMakeFiles\oatpp.dir\oatpp\core\base\Environment.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\base\Environment.cpp -o CMakeFiles\oatpp.dir\oatpp\core\base\Environment.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/concurrency/SpinLock.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\concurrency\SpinLock.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\concurrency\SpinLock.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\SpinLock.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\SpinLock.cpp > CMakeFiles\oatpp.dir\oatpp\core\concurrency\SpinLock.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\SpinLock.cpp -o CMakeFiles\oatpp.dir\oatpp\core\concurrency\SpinLock.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/concurrency/Thread.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\concurrency\Thread.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\concurrency\Thread.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\Thread.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\Thread.cpp > CMakeFiles\oatpp.dir\oatpp\core\concurrency\Thread.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\concurrency\Thread.cpp -o CMakeFiles\oatpp.dir\oatpp\core\concurrency\Thread.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/Bundle.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\Bundle.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\Bundle.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\Bundle.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\Bundle.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\Bundle.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\Bundle.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\Bundle.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/FIFOBuffer.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\buffer\FIFOBuffer.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\FIFOBuffer.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\FIFOBuffer.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\FIFOBuffer.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\buffer\FIFOBuffer.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\FIFOBuffer.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\FIFOBuffer.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/IOBuffer.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\buffer\IOBuffer.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\IOBuffer.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\IOBuffer.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\IOBuffer.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\buffer\IOBuffer.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\IOBuffer.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\IOBuffer.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/Processor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\buffer\Processor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\Processor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\Processor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\Processor.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\buffer\Processor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\buffer\Processor.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\buffer\Processor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/ObjectMapper.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\ObjectMapper.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\ObjectMapper.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\ObjectMapper.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\ObjectMapper.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\ObjectMapper.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\ObjectMapper.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\ObjectMapper.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/TypeResolver.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\TypeResolver.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\TypeResolver.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\TypeResolver.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\TypeResolver.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\TypeResolver.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\TypeResolver.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\TypeResolver.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Any.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Any.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Any.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Any.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Any.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Any.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Any.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Any.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Enum.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Enum.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Enum.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Enum.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Enum.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Enum.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Enum.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Enum.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/List.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\List.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\List.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\List.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\List.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\List.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\List.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\List.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Object.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Object.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Object.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Object.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Object.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Object.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Object.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Object.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/PairList.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\PairList.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\PairList.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\PairList.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\PairList.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\PairList.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\PairList.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\PairList.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Primitive.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Primitive.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Primitive.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Primitive.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Primitive.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Primitive.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Primitive.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Primitive.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Type.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Type.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Type.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Type.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Type.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Type.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Type.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Type.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedMap.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedMap.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedMap.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedMap.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedMap.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedMap.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedMap.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedMap.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedSet.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedSet.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedSet.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedSet.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedSet.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedSet.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\UnorderedSet.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\UnorderedSet.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Vector.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Vector.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Vector.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Vector.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Vector.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Vector.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\mapping\type\Vector.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\mapping\type\Vector.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/File.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\resource\File.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\File.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\File.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\File.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\resource\File.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\File.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\File.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/InMemoryData.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\resource\InMemoryData.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\InMemoryData.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\InMemoryData.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\InMemoryData.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\resource\InMemoryData.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\InMemoryData.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\InMemoryData.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/resource/TemporaryFile.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\resource\TemporaryFile.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\TemporaryFile.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\TemporaryFile.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\TemporaryFile.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\resource\TemporaryFile.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\resource\TemporaryFile.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\resource\TemporaryFile.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/MemoryLabel.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\share\MemoryLabel.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\share\MemoryLabel.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\MemoryLabel.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\MemoryLabel.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\share\MemoryLabel.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\MemoryLabel.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\share\MemoryLabel.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/StringTemplate.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\share\StringTemplate.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\share\StringTemplate.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\StringTemplate.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\StringTemplate.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\share\StringTemplate.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\share\StringTemplate.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\share\StringTemplate.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/BufferStream.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\stream\BufferStream.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\BufferStream.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\BufferStream.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\BufferStream.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\stream\BufferStream.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\BufferStream.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\BufferStream.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/FIFOStream.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\stream\FIFOStream.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\FIFOStream.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FIFOStream.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FIFOStream.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\stream\FIFOStream.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FIFOStream.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\FIFOStream.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/FileStream.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\stream\FileStream.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\FileStream.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FileStream.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FileStream.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\stream\FileStream.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\FileStream.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\FileStream.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/Stream.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\stream\Stream.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\Stream.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\Stream.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\Stream.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\stream\Stream.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\Stream.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\Stream.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/StreamBufferedProxy.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\data\stream\StreamBufferedProxy.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\StreamBufferedProxy.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\StreamBufferedProxy.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\StreamBufferedProxy.cpp > CMakeFiles\oatpp.dir\oatpp\core\data\stream\StreamBufferedProxy.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\data\stream\StreamBufferedProxy.cpp -o CMakeFiles\oatpp.dir\oatpp\core\data\stream\StreamBufferedProxy.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/parser/Caret.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\parser\Caret.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\parser\Caret.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\Caret.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\Caret.cpp > CMakeFiles\oatpp.dir\oatpp\core\parser\Caret.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\Caret.cpp -o CMakeFiles\oatpp.dir\oatpp\core\parser\Caret.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/parser/ParsingError.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\parser\ParsingError.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\parser\ParsingError.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\ParsingError.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\ParsingError.cpp > CMakeFiles\oatpp.dir\oatpp\core\parser\ParsingError.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\parser\ParsingError.cpp -o CMakeFiles\oatpp.dir\oatpp\core\parser\ParsingError.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/Binary.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\utils\Binary.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\utils\Binary.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Binary.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Binary.cpp > CMakeFiles\oatpp.dir\oatpp\core\utils\Binary.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Binary.cpp -o CMakeFiles\oatpp.dir\oatpp\core\utils\Binary.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/ConversionUtils.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\utils\ConversionUtils.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\utils\ConversionUtils.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\ConversionUtils.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\ConversionUtils.cpp > CMakeFiles\oatpp.dir\oatpp\core\utils\ConversionUtils.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\ConversionUtils.cpp -o CMakeFiles\oatpp.dir\oatpp\core\utils\ConversionUtils.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/Random.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\utils\Random.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\utils\Random.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Random.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Random.cpp > CMakeFiles\oatpp.dir\oatpp\core\utils\Random.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\Random.cpp -o CMakeFiles\oatpp.dir\oatpp\core\utils\Random.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/String.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\core\utils\String.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\core\utils\String.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\String.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\String.cpp > CMakeFiles\oatpp.dir\oatpp\core\utils\String.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\core\utils\String.cpp -o CMakeFiles\oatpp.dir\oatpp\core\utils\String.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Base64.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\encoding\Base64.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\encoding\Base64.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Base64.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Base64.cpp > CMakeFiles\oatpp.dir\oatpp\encoding\Base64.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Base64.cpp -o CMakeFiles\oatpp.dir\oatpp\encoding\Base64.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Hex.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\encoding\Hex.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\encoding\Hex.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Hex.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Hex.cpp > CMakeFiles\oatpp.dir\oatpp\encoding\Hex.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Hex.cpp -o CMakeFiles\oatpp.dir\oatpp\encoding\Hex.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Unicode.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\encoding\Unicode.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\encoding\Unicode.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Unicode.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Unicode.cpp > CMakeFiles\oatpp.dir\oatpp\encoding\Unicode.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Unicode.cpp -o CMakeFiles\oatpp.dir\oatpp\encoding\Unicode.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/encoding/Url.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\encoding\Url.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\encoding\Url.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Url.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Url.cpp > CMakeFiles\oatpp.dir\oatpp\encoding\Url.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\encoding\Url.cpp -o CMakeFiles\oatpp.dir\oatpp\encoding\Url.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/Address.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\Address.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\Address.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Address.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Address.cpp > CMakeFiles\oatpp.dir\oatpp\network\Address.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Address.cpp -o CMakeFiles\oatpp.dir\oatpp\network\Address.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionPool.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\ConnectionPool.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionPool.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionPool.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionPool.cpp > CMakeFiles\oatpp.dir\oatpp\network\ConnectionPool.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionPool.cpp -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionPool.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProvider.cpp > CMakeFiles\oatpp.dir\oatpp\network\ConnectionProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionProviderSwitch.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\ConnectionProviderSwitch.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionProviderSwitch.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProviderSwitch.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProviderSwitch.cpp > CMakeFiles\oatpp.dir\oatpp\network\ConnectionProviderSwitch.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\ConnectionProviderSwitch.cpp -o CMakeFiles\oatpp.dir\oatpp\network\ConnectionProviderSwitch.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/Server.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\Server.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\Server.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Server.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Server.cpp > CMakeFiles\oatpp.dir\oatpp\network\Server.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Server.cpp -o CMakeFiles\oatpp.dir\oatpp\network\Server.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/Url.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\Url.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\Url.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Url.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Url.cpp > CMakeFiles\oatpp.dir\oatpp\network\Url.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\Url.cpp -o CMakeFiles\oatpp.dir\oatpp\network\Url.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionInactivityChecker.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionInactivityChecker.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionInactivityChecker.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionInactivityChecker.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionInactivityChecker.cpp > CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionInactivityChecker.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionInactivityChecker.cpp -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionInactivityChecker.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp > CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMaxAgeChecker.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/monitor/ConnectionMonitor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMonitor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMonitor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMonitor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMonitor.cpp > CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMonitor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\monitor\ConnectionMonitor.cpp -o CMakeFiles\oatpp.dir\oatpp\network\monitor\ConnectionMonitor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/Connection.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\tcp\Connection.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\tcp\Connection.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\Connection.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\Connection.cpp > CMakeFiles\oatpp.dir\oatpp\network\tcp\Connection.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\Connection.cpp -o CMakeFiles\oatpp.dir\oatpp\network\tcp\Connection.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/client/ConnectionProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\tcp\client\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\tcp\client\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\client\ConnectionProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\client\ConnectionProvider.cpp > CMakeFiles\oatpp.dir\oatpp\network\tcp\client\ConnectionProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\client\ConnectionProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\network\tcp\client\ConnectionProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/server/ConnectionProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\tcp\server\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\tcp\server\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\server\ConnectionProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\server\ConnectionProvider.cpp > CMakeFiles\oatpp.dir\oatpp\network\tcp\server\ConnectionProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\server\ConnectionProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\network\tcp\server\ConnectionProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Interface.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\virtual_\Interface.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Interface.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Interface.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Interface.cpp > CMakeFiles\oatpp.dir\oatpp\network\virtual_\Interface.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Interface.cpp -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Interface.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Pipe.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\virtual_\Pipe.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Pipe.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Pipe.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Pipe.cpp > CMakeFiles\oatpp.dir\oatpp\network\virtual_\Pipe.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Pipe.cpp -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Pipe.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/Socket.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\virtual_\Socket.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Socket.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Socket.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Socket.cpp > CMakeFiles\oatpp.dir\oatpp\network\virtual_\Socket.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\Socket.cpp -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\Socket.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/client/ConnectionProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\virtual_\client\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\client\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\client\ConnectionProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\client\ConnectionProvider.cpp > CMakeFiles\oatpp.dir\oatpp\network\virtual_\client\ConnectionProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\client\ConnectionProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\client\ConnectionProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/network/virtual_/server/ConnectionProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\network\virtual_\server\ConnectionProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\server\ConnectionProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\server\ConnectionProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\server\ConnectionProvider.cpp > CMakeFiles\oatpp.dir\oatpp\network\virtual_\server\ConnectionProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\network\virtual_\server\ConnectionProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\network\virtual_\server\ConnectionProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/orm/DbClient.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\orm\DbClient.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\orm\DbClient.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\DbClient.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\DbClient.cpp > CMakeFiles\oatpp.dir\oatpp\orm\DbClient.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\DbClient.cpp -o CMakeFiles\oatpp.dir\oatpp\orm\DbClient.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/orm/Executor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\orm\Executor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\orm\Executor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Executor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Executor.cpp > CMakeFiles\oatpp.dir\oatpp\orm\Executor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Executor.cpp -o CMakeFiles\oatpp.dir\oatpp\orm\Executor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/orm/QueryResult.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\orm\QueryResult.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\orm\QueryResult.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\QueryResult.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\QueryResult.cpp > CMakeFiles\oatpp.dir\oatpp\orm\QueryResult.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\QueryResult.cpp -o CMakeFiles\oatpp.dir\oatpp\orm\QueryResult.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/orm/SchemaMigration.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\orm\SchemaMigration.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\orm\SchemaMigration.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\SchemaMigration.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\SchemaMigration.cpp > CMakeFiles\oatpp.dir\oatpp\orm\SchemaMigration.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\SchemaMigration.cpp -o CMakeFiles\oatpp.dir\oatpp\orm\SchemaMigration.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/orm/Transaction.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\orm\Transaction.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\orm\Transaction.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Transaction.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Transaction.cpp > CMakeFiles\oatpp.dir\oatpp\orm\Transaction.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\orm\Transaction.cpp -o CMakeFiles\oatpp.dir\oatpp\orm\Transaction.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/Beautifier.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\parser\json\Beautifier.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\parser\json\Beautifier.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Beautifier.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Beautifier.cpp > CMakeFiles\oatpp.dir\oatpp\parser\json\Beautifier.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Beautifier.cpp -o CMakeFiles\oatpp.dir\oatpp\parser\json\Beautifier.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/Utils.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\parser\json\Utils.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\parser\json\Utils.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Utils.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Utils.cpp > CMakeFiles\oatpp.dir\oatpp\parser\json\Utils.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\Utils.cpp -o CMakeFiles\oatpp.dir\oatpp\parser\json\Utils.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/Deserializer.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Deserializer.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Deserializer.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Deserializer.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Deserializer.cpp > CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Deserializer.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Deserializer.cpp -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Deserializer.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/ObjectMapper.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\ObjectMapper.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\ObjectMapper.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\ObjectMapper.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\ObjectMapper.cpp > CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\ObjectMapper.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\ObjectMapper.cpp -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\ObjectMapper.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/parser/json/mapping/Serializer.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Serializer.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Serializer.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Serializer.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Serializer.cpp > CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Serializer.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\parser\json\mapping\Serializer.cpp -o CMakeFiles\oatpp.dir\oatpp\parser\json\mapping\Serializer.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/ApiClient.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\client\ApiClient.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\client\ApiClient.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\ApiClient.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\ApiClient.cpp > CMakeFiles\oatpp.dir\oatpp\web\client\ApiClient.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\ApiClient.cpp -o CMakeFiles\oatpp.dir\oatpp\web\client\ApiClient.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/HttpRequestExecutor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\client\HttpRequestExecutor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\client\HttpRequestExecutor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\HttpRequestExecutor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\HttpRequestExecutor.cpp > CMakeFiles\oatpp.dir\oatpp\web\client\HttpRequestExecutor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\HttpRequestExecutor.cpp -o CMakeFiles\oatpp.dir\oatpp\web\client\HttpRequestExecutor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/RequestExecutor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\client\RequestExecutor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\client\RequestExecutor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RequestExecutor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RequestExecutor.cpp > CMakeFiles\oatpp.dir\oatpp\web\client\RequestExecutor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RequestExecutor.cpp -o CMakeFiles\oatpp.dir\oatpp\web\client\RequestExecutor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/client/RetryPolicy.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\client\RetryPolicy.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\client\RetryPolicy.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RetryPolicy.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RetryPolicy.cpp > CMakeFiles\oatpp.dir\oatpp\web\client\RetryPolicy.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\client\RetryPolicy.cpp -o CMakeFiles\oatpp.dir\oatpp\web\client\RetryPolicy.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/FileProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\FileProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\FileProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\FileProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\FileProvider.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\FileProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\FileProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\FileProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/InMemoryDataProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\InMemoryDataProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\InMemoryDataProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\InMemoryDataProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\InMemoryDataProvider.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\InMemoryDataProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\InMemoryDataProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\InMemoryDataProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Multipart.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Multipart.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Multipart.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Multipart.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Multipart.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Multipart.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Multipart.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Multipart.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Part.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Part.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Part.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Part.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Part.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Part.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Part.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Part.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/PartList.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartList.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartList.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartList.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartList.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartList.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartList.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartList.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/PartReader.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartReader.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartReader.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartReader.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartReader.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartReader.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\PartReader.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\PartReader.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/Reader.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Reader.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Reader.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Reader.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Reader.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Reader.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\Reader.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\Reader.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/StatefulParser.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\StatefulParser.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\StatefulParser.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\StatefulParser.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\StatefulParser.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\StatefulParser.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\StatefulParser.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\StatefulParser.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/mime/multipart/TemporaryFileProvider.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\TemporaryFileProvider.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\TemporaryFileProvider.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\TemporaryFileProvider.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\TemporaryFileProvider.cpp > CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\TemporaryFileProvider.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\mime\multipart\TemporaryFileProvider.cpp -o CMakeFiles\oatpp.dir\oatpp\web\mime\multipart\TemporaryFileProvider.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/CommunicationError.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\CommunicationError.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\CommunicationError.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\CommunicationError.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\CommunicationError.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\CommunicationError.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\CommunicationError.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\CommunicationError.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/Http.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\Http.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\Http.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\Http.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\Http.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\Http.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\Http.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\Http.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/encoding/Chunked.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\Chunked.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\Chunked.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\Chunked.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\Chunked.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\Chunked.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\Chunked.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\Chunked.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/encoding/ProviderCollection.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\ProviderCollection.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\ProviderCollection.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\ProviderCollection.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\ProviderCollection.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\ProviderCollection.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\encoding\ProviderCollection.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\encoding\ProviderCollection.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/BodyDecoder.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\BodyDecoder.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\BodyDecoder.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\BodyDecoder.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\BodyDecoder.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\BodyDecoder.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\BodyDecoder.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\BodyDecoder.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/Request.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Request.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Request.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Request.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Request.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Request.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Request.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Request.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\RequestHeadersReader.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/Response.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Response.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Response.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Response.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Response.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Response.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\Response.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\Response.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\ResponseHeadersReader.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\incoming\SimpleBodyDecoder.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Body.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Body.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Body.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Body.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Body.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Body.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Body.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Body.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/BufferBody.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\BufferBody.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\BufferBody.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\BufferBody.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\BufferBody.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\BufferBody.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\BufferBody.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\BufferBody.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/MultipartBody.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\MultipartBody.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\MultipartBody.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\MultipartBody.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\MultipartBody.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\MultipartBody.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\MultipartBody.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\MultipartBody.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Request.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Request.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Request.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Request.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Request.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Request.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Request.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Request.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/Response.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Response.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Response.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Response.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Response.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Response.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\Response.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\Response.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\ResponseFactory.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/outgoing/StreamingBody.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\StreamingBody.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\StreamingBody.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\StreamingBody.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\StreamingBody.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\StreamingBody.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\outgoing\StreamingBody.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\outgoing\StreamingBody.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/protocol/http/utils/CommunicationUtils.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils\CommunicationUtils.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils\CommunicationUtils.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\utils\CommunicationUtils.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\utils\CommunicationUtils.cpp > CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils\CommunicationUtils.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\protocol\http\utils\CommunicationUtils.cpp -o CMakeFiles\oatpp.dir\oatpp\web\protocol\http\utils\CommunicationUtils.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/AsyncHttpConnectionHandler.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_116) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\AsyncHttpConnectionHandler.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\AsyncHttpConnectionHandler.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\AsyncHttpConnectionHandler.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\AsyncHttpConnectionHandler.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\AsyncHttpConnectionHandler.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\AsyncHttpConnectionHandler.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\AsyncHttpConnectionHandler.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpConnectionHandler.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_117) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\HttpConnectionHandler.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpConnectionHandler.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpConnectionHandler.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpConnectionHandler.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\HttpConnectionHandler.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpConnectionHandler.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpConnectionHandler.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpProcessor.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_118) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\HttpProcessor.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpProcessor.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpProcessor.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpProcessor.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\HttpProcessor.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpProcessor.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpProcessor.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/HttpRouter.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_119) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\HttpRouter.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpRouter.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpRouter.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpRouter.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\HttpRouter.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\HttpRouter.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\HttpRouter.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/api/ApiController.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_120) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\api\ApiController.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\api\ApiController.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\ApiController.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\ApiController.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\api\ApiController.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\ApiController.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\api\ApiController.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/api/Endpoint.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_121) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\api\Endpoint.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\api\Endpoint.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\Endpoint.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\Endpoint.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\api\Endpoint.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\api\Endpoint.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\api\Endpoint.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/handler/AuthorizationHandler.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_122) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\handler\AuthorizationHandler.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\handler\AuthorizationHandler.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\AuthorizationHandler.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\AuthorizationHandler.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\handler\AuthorizationHandler.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\AuthorizationHandler.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\handler\AuthorizationHandler.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/handler/ErrorHandler.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_123) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\handler\ErrorHandler.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\handler\ErrorHandler.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\ErrorHandler.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\ErrorHandler.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\handler\ErrorHandler.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\handler\ErrorHandler.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\handler\ErrorHandler.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/server/interceptor/AllowCorsGlobal.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_124) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\server\interceptor\AllowCorsGlobal.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\server\interceptor\AllowCorsGlobal.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\interceptor\AllowCorsGlobal.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\interceptor\AllowCorsGlobal.cpp > CMakeFiles\oatpp.dir\oatpp\web\server\interceptor\AllowCorsGlobal.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\server\interceptor\AllowCorsGlobal.cpp -o CMakeFiles\oatpp.dir\oatpp\web\server\interceptor\AllowCorsGlobal.cpp.s

Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj: Oat++/CMakeFiles/oatpp.dir/flags.make
Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj: Oat++/CMakeFiles/oatpp.dir/includes_CXX.rsp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj: D:/MiWebApp/WebServerApp/Oat++/oatpp/web/url/mapping/Pattern.cpp
Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj: Oat++/CMakeFiles/oatpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_125) "Building CXX object Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj -MF CMakeFiles\oatpp.dir\oatpp\web\url\mapping\Pattern.cpp.obj.d -o CMakeFiles\oatpp.dir\oatpp\web\url\mapping\Pattern.cpp.obj -c D:\MiWebApp\WebServerApp\Oat++\oatpp\web\url\mapping\Pattern.cpp

Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.i"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\MiWebApp\WebServerApp\Oat++\oatpp\web\url\mapping\Pattern.cpp > CMakeFiles\oatpp.dir\oatpp\web\url\mapping\Pattern.cpp.i

Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.s"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && C:\mingw810_64\bin\c++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\MiWebApp\WebServerApp\Oat++\oatpp\web\url\mapping\Pattern.cpp -o CMakeFiles\oatpp.dir\oatpp\web\url\mapping\Pattern.cpp.s

# Object files for target oatpp
oatpp_OBJECTS = \
"CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj" \
"CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj"

# External object files for target oatpp
oatpp_EXTERNAL_OBJECTS =

Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/build.make
Oat++/liboatpp.a: Oat++/CMakeFiles/oatpp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\MiWebApp\WebServerApp\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_126) "Linking CXX static library liboatpp.a"
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && $(CMAKE_COMMAND) -P CMakeFiles\oatpp.dir\cmake_clean_target.cmake
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\oatpp.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
Oat++/CMakeFiles/oatpp.dir/build: Oat++/liboatpp.a
.PHONY : Oat++/CMakeFiles/oatpp.dir/build

Oat++/CMakeFiles/oatpp.dir/clean:
	cd /d D:\MiWebApp\WebServerApp\build\Oat++ && $(CMAKE_COMMAND) -P CMakeFiles\oatpp.dir\cmake_clean.cmake
.PHONY : Oat++/CMakeFiles/oatpp.dir/clean

Oat++/CMakeFiles/oatpp.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\MiWebApp\WebServerApp D:\MiWebApp\WebServerApp\Oat++ D:\MiWebApp\WebServerApp\build D:\MiWebApp\WebServerApp\build\Oat++ D:\MiWebApp\WebServerApp\build\Oat++\CMakeFiles\oatpp.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : Oat++/CMakeFiles/oatpp.dir/depend

