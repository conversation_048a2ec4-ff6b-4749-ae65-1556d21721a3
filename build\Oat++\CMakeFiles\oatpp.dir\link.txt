C:\mingw810_64\bin\ar.exe qc liboatpp.a CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj
C:\mingw810_64\bin\ar.exe q liboatpp.a CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj
C:\mingw810_64\bin\ranlib.exe liboatpp.a
