Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj: \
 D:\MiWebApp\WebServerApp\Oat++\oatpp\network\tcp\client\ConnectionProvider.cpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/client/ConnectionProvider.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/network/Address.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/Types.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Object.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Type.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Countable.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/memory \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_algobase.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/functexcept.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception_defines.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cpp_type_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/type_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/numeric_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_pair.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/move.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/concept_check.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/type_traits \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator_base_types.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator_base_funcs.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/debug/assertions.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_iterator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ptr_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/debug/debug.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/predefined_ops.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/allocator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/new_allocator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/new \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/exception \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/exception_ptr.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cxxabi_init_exception.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stddef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stddef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/crtdefs.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_mac.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_secapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/vadefs.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sdks/_mingw_directx.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/typeinfo \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/hash_bytes.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/nested_exception.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/memoryfwd.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_construct.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/alloc_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/alloc_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_uninitialized.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_tempbuf.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_raw_storage_iter.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/iosfwd \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stringfwd.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/postypes.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cwchar \
 C:/mingw810_64/x86_64-w64-mingw32/include/wchar.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_print_push.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_off_t.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_stat64.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/swprintf.inl \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/wchar_s.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_print_pop.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/atomicity.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pthread.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/errno.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sys/types.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/process.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/corecrt_startup.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed/limits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed/syslimits.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/limits.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/signal.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pthread_signal.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sys/timeb.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pthread_compat.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pthread_unistd.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/concurrence.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_function.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward/binders.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/uses_allocator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/unique_ptr.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/utility \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_relops.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/initializer_list \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/tuple \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/array \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/stdexcept \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/string \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/char_traits.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdint \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stdint.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stdint.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/localefwd.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/clocale \
 C:/mingw810_64/x86_64-w64-mingw32/include/locale.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stdio.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/stdio_s.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cctype \
 C:/mingw810_64/x86_64-w64-mingw32/include/ctype.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ostream_insert.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/cxxabi_forced.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/range_access.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_string.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/string_conversions.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdlib \
 C:/mingw810_64/x86_64-w64-mingw32/include/stdlib.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/stdlib.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/malloc.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/std_abs.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdio \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cerrno \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/functional_hash.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_string.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/invoke.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr_base.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/allocated_ptr.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/refwrap.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ext/aligned_buffer.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/shared_ptr_atomic.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/atomic_base.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/atomic_lockfree_defines.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward/auto_ptr.h \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Environment.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Compiler.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Config.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstdarg \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/stdarg.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stdarg.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_stdarg.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/atomic \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/mutex \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/chrono \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ratio \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/limits \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ctime \
 C:/mingw810_64/x86_64-w64-mingw32/include/time.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_timeval.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pthread_time.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/parse_numbers.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/system_error \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/std_mutex.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/unordered_map \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/hashtable.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/hashtable_policy.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/unordered_map.h \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/Environment.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/list \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_list.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/list.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/vector \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_vector.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_bvector.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/vector.tcc \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Any.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Primitive.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/algorithm \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_algo.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/algorithmfwd.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stl_heap.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/uniform_int_dist.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/iterator \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ostream \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/ios \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ios_base.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_classes.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_classes.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/streambuf \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/streambuf.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_ios.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_facets.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cwctype \
 C:/mingw810_64/x86_64-w64-mingw32/include/wctype.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/streambuf_iterator.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/locale_facets.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/basic_ios.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/ostream.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/istream \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/istream.tcc \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/stream_iterator.h \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Enum.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/MemoryLabel.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Primitive.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/String.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/cstring \
 C:/mingw810_64/x86_64-w64-mingw32/include/string.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/string_s.h \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedMap.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Map.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/PairList.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/List.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Collection.hpp \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/unordered_set \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/bits/unordered_set.h \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/Vector.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/mapping/type/UnorderedSet.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/network/ConnectionProvider.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/stream/Stream.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/LazyStringMap.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/share/MemoryLabel.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/concurrency/SpinLock.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Coroutine.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Error.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/utils/FastQueue.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/IODefinitions.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/async/Error.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/IOBuffer.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/data/buffer/Processor.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/base/ObjectHandle.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/provider/Provider.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/provider/Invalidator.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/provider/Invalidator.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/network/tcp/Connection.hpp \
 D:/MiWebApp/WebServerApp/Oat++/oatpp/core/utils/ConversionUtils.hpp \
 C:/mingw810_64/x86_64-w64-mingw32/include/fcntl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/io.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winsock2.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_mingw_unicode.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/windows.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sdkddkver.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/excpt.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/windef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/minwindef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winapifamily.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/specstrings.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sal.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/driverspecs.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winnt.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/apiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/intrin-impl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/basetsd.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/guiddef.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/x86intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/ia32intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/mmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xmmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/mm_malloc.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/emmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/pmmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/tmmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/ammintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/smmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/popcntintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/wmmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/immintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avxintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx2intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512fintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512erintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512pfintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512cdintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512bwintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512dqintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vlbwintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vldqintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512ifmaintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512ifmavlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vbmiintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vbmivlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx5124fmapsintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx5124vnniwintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vpopcntdqintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vbmi2intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vbmi2vlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vnniintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vnnivlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512vpopcntdqvlintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/avx512bitalgintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/shaintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/lzcntintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/bmiintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/bmi2intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/fmaintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/f16cintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/rtmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xtestintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/cetintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/gfniintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/vaesintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/vpclmulqdqintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/movdirintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/mm3dnow.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/prfchwintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/fma4intrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xopintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/lwpintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/tbmintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/rdseedintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/fxsrintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xsaveintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xsaveoptintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/sgxintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/pconfigintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/adxintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/clwbintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/clflushoptintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xsavesintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/xsavecintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/mwaitxintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/clzerointrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/wbnoinvdintrin.h \
 C:/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/pkuintrin.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack4.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/poppack.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack4.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack2.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/poppack.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack2.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack8.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack8.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ktmtypes.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/apisetcconv.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/minwinbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/bemapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/debugapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/errhandlingapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/fibersapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/fileapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/handleapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/heapapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ioapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/interlockedapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/jobapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/libloaderapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/memoryapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/namedpipeapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/namespaceapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/processenv.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/processthreadsapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/processtopologyapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/profileapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/realtimeapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/securityappcontainer.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/securitybaseapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/synchapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sysinfoapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/systemtopologyapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/threadpoolapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/threadpoollegacyapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/utilapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wow64apiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winerror.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/fltwinerror.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/timezoneapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wingdi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/pshpack1.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winuser.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/tvout.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winnls.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/datetimeapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stringapiset.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wincon.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winver.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winreg.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/reason.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winnetwk.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wnnc.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/virtdisk.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/cderr.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/dde.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ddeml.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/dlgs.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/lzexpand.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/mmsystem.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/nb30.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpc.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcdce.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcdcep.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcnsi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcnterr.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcasync.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/shellapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winperf.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winsock.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wincrypt.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/bcrypt.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ncrypt.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/dpapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winefs.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winscard.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wtypes.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcndr.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcnsip.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/rpcsal.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ole2.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/objbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/combaseapi.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/wtypesbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/unknwnbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/objidlbase.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/cguid.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/objidl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/unknwn.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/urlmon.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/oleidl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/servprov.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/msxml.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/oaidl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/propidl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/oleauto.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winioctl.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winsmcrd.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winspool.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/prsht.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/commdlg.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/stralign.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/sec_api/stralign_s.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/winsvc.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/mcx.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/imm.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_ws1_undef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/_bsd_types.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/inaddr.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_socket_types.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_fd_types.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_ip_types.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_wsadata.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ws2def.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_wsa_errnos.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/qos.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ws2tcpip.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/ws2ipdef.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/in6addr.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/psdk_inc/_ip_mreq1.h \
 C:/mingw810_64/x86_64-w64-mingw32/include/mstcpip.h
