# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\MiWebApp\WebServerApp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\MiWebApp\WebServerApp\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\Oat++\\CMakeFiles\progress.marks
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles D:\MiWebApp\WebServerApp\build\Oat++\\CMakeFiles\progress.marks
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/codegen
	$(CMAKE_COMMAND) -E cmake_progress_start D:\MiWebApp\WebServerApp\build\CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
Oat++/CMakeFiles/oatpp.dir/rule:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Oat++/CMakeFiles/oatpp.dir/rule
.PHONY : Oat++/CMakeFiles/oatpp.dir/rule

# Convenience name for target.
oatpp: Oat++/CMakeFiles/oatpp.dir/rule
.PHONY : oatpp

# fast build rule for target.
oatpp/fast:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/build
.PHONY : oatpp/fast

oatpp/algorithm/CRC.obj: oatpp/algorithm/CRC.cpp.obj
.PHONY : oatpp/algorithm/CRC.obj

# target to build an object file
oatpp/algorithm/CRC.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.obj
.PHONY : oatpp/algorithm/CRC.cpp.obj

oatpp/algorithm/CRC.i: oatpp/algorithm/CRC.cpp.i
.PHONY : oatpp/algorithm/CRC.i

# target to preprocess a source file
oatpp/algorithm/CRC.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.i
.PHONY : oatpp/algorithm/CRC.cpp.i

oatpp/algorithm/CRC.s: oatpp/algorithm/CRC.cpp.s
.PHONY : oatpp/algorithm/CRC.s

# target to generate assembly for a file
oatpp/algorithm/CRC.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/algorithm/CRC.cpp.s
.PHONY : oatpp/algorithm/CRC.cpp.s

oatpp/core/IODefinitions.obj: oatpp/core/IODefinitions.cpp.obj
.PHONY : oatpp/core/IODefinitions.obj

# target to build an object file
oatpp/core/IODefinitions.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.obj
.PHONY : oatpp/core/IODefinitions.cpp.obj

oatpp/core/IODefinitions.i: oatpp/core/IODefinitions.cpp.i
.PHONY : oatpp/core/IODefinitions.i

# target to preprocess a source file
oatpp/core/IODefinitions.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.i
.PHONY : oatpp/core/IODefinitions.cpp.i

oatpp/core/IODefinitions.s: oatpp/core/IODefinitions.cpp.s
.PHONY : oatpp/core/IODefinitions.s

# target to generate assembly for a file
oatpp/core/IODefinitions.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/IODefinitions.cpp.s
.PHONY : oatpp/core/IODefinitions.cpp.s

oatpp/core/async/ConditionVariable.obj: oatpp/core/async/ConditionVariable.cpp.obj
.PHONY : oatpp/core/async/ConditionVariable.obj

# target to build an object file
oatpp/core/async/ConditionVariable.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.obj
.PHONY : oatpp/core/async/ConditionVariable.cpp.obj

oatpp/core/async/ConditionVariable.i: oatpp/core/async/ConditionVariable.cpp.i
.PHONY : oatpp/core/async/ConditionVariable.i

# target to preprocess a source file
oatpp/core/async/ConditionVariable.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.i
.PHONY : oatpp/core/async/ConditionVariable.cpp.i

oatpp/core/async/ConditionVariable.s: oatpp/core/async/ConditionVariable.cpp.s
.PHONY : oatpp/core/async/ConditionVariable.s

# target to generate assembly for a file
oatpp/core/async/ConditionVariable.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/ConditionVariable.cpp.s
.PHONY : oatpp/core/async/ConditionVariable.cpp.s

oatpp/core/async/Coroutine.obj: oatpp/core/async/Coroutine.cpp.obj
.PHONY : oatpp/core/async/Coroutine.obj

# target to build an object file
oatpp/core/async/Coroutine.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.obj
.PHONY : oatpp/core/async/Coroutine.cpp.obj

oatpp/core/async/Coroutine.i: oatpp/core/async/Coroutine.cpp.i
.PHONY : oatpp/core/async/Coroutine.i

# target to preprocess a source file
oatpp/core/async/Coroutine.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.i
.PHONY : oatpp/core/async/Coroutine.cpp.i

oatpp/core/async/Coroutine.s: oatpp/core/async/Coroutine.cpp.s
.PHONY : oatpp/core/async/Coroutine.s

# target to generate assembly for a file
oatpp/core/async/Coroutine.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Coroutine.cpp.s
.PHONY : oatpp/core/async/Coroutine.cpp.s

oatpp/core/async/CoroutineWaitList.obj: oatpp/core/async/CoroutineWaitList.cpp.obj
.PHONY : oatpp/core/async/CoroutineWaitList.obj

# target to build an object file
oatpp/core/async/CoroutineWaitList.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.obj
.PHONY : oatpp/core/async/CoroutineWaitList.cpp.obj

oatpp/core/async/CoroutineWaitList.i: oatpp/core/async/CoroutineWaitList.cpp.i
.PHONY : oatpp/core/async/CoroutineWaitList.i

# target to preprocess a source file
oatpp/core/async/CoroutineWaitList.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.i
.PHONY : oatpp/core/async/CoroutineWaitList.cpp.i

oatpp/core/async/CoroutineWaitList.s: oatpp/core/async/CoroutineWaitList.cpp.s
.PHONY : oatpp/core/async/CoroutineWaitList.s

# target to generate assembly for a file
oatpp/core/async/CoroutineWaitList.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/CoroutineWaitList.cpp.s
.PHONY : oatpp/core/async/CoroutineWaitList.cpp.s

oatpp/core/async/Error.obj: oatpp/core/async/Error.cpp.obj
.PHONY : oatpp/core/async/Error.obj

# target to build an object file
oatpp/core/async/Error.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.obj
.PHONY : oatpp/core/async/Error.cpp.obj

oatpp/core/async/Error.i: oatpp/core/async/Error.cpp.i
.PHONY : oatpp/core/async/Error.i

# target to preprocess a source file
oatpp/core/async/Error.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.i
.PHONY : oatpp/core/async/Error.cpp.i

oatpp/core/async/Error.s: oatpp/core/async/Error.cpp.s
.PHONY : oatpp/core/async/Error.s

# target to generate assembly for a file
oatpp/core/async/Error.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Error.cpp.s
.PHONY : oatpp/core/async/Error.cpp.s

oatpp/core/async/Executor.obj: oatpp/core/async/Executor.cpp.obj
.PHONY : oatpp/core/async/Executor.obj

# target to build an object file
oatpp/core/async/Executor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.obj
.PHONY : oatpp/core/async/Executor.cpp.obj

oatpp/core/async/Executor.i: oatpp/core/async/Executor.cpp.i
.PHONY : oatpp/core/async/Executor.i

# target to preprocess a source file
oatpp/core/async/Executor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.i
.PHONY : oatpp/core/async/Executor.cpp.i

oatpp/core/async/Executor.s: oatpp/core/async/Executor.cpp.s
.PHONY : oatpp/core/async/Executor.s

# target to generate assembly for a file
oatpp/core/async/Executor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Executor.cpp.s
.PHONY : oatpp/core/async/Executor.cpp.s

oatpp/core/async/Lock.obj: oatpp/core/async/Lock.cpp.obj
.PHONY : oatpp/core/async/Lock.obj

# target to build an object file
oatpp/core/async/Lock.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.obj
.PHONY : oatpp/core/async/Lock.cpp.obj

oatpp/core/async/Lock.i: oatpp/core/async/Lock.cpp.i
.PHONY : oatpp/core/async/Lock.i

# target to preprocess a source file
oatpp/core/async/Lock.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.i
.PHONY : oatpp/core/async/Lock.cpp.i

oatpp/core/async/Lock.s: oatpp/core/async/Lock.cpp.s
.PHONY : oatpp/core/async/Lock.s

# target to generate assembly for a file
oatpp/core/async/Lock.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Lock.cpp.s
.PHONY : oatpp/core/async/Lock.cpp.s

oatpp/core/async/Processor.obj: oatpp/core/async/Processor.cpp.obj
.PHONY : oatpp/core/async/Processor.obj

# target to build an object file
oatpp/core/async/Processor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.obj
.PHONY : oatpp/core/async/Processor.cpp.obj

oatpp/core/async/Processor.i: oatpp/core/async/Processor.cpp.i
.PHONY : oatpp/core/async/Processor.i

# target to preprocess a source file
oatpp/core/async/Processor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.i
.PHONY : oatpp/core/async/Processor.cpp.i

oatpp/core/async/Processor.s: oatpp/core/async/Processor.cpp.s
.PHONY : oatpp/core/async/Processor.s

# target to generate assembly for a file
oatpp/core/async/Processor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/Processor.cpp.s
.PHONY : oatpp/core/async/Processor.cpp.s

oatpp/core/async/worker/IOEventWorker_common.obj: oatpp/core/async/worker/IOEventWorker_common.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_common.obj

# target to build an object file
oatpp/core/async/worker/IOEventWorker_common.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_common.cpp.obj

oatpp/core/async/worker/IOEventWorker_common.i: oatpp/core/async/worker/IOEventWorker_common.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_common.i

# target to preprocess a source file
oatpp/core/async/worker/IOEventWorker_common.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_common.cpp.i

oatpp/core/async/worker/IOEventWorker_common.s: oatpp/core/async/worker/IOEventWorker_common.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_common.s

# target to generate assembly for a file
oatpp/core/async/worker/IOEventWorker_common.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_common.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_common.cpp.s

oatpp/core/async/worker/IOEventWorker_epoll.obj: oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.obj

# target to build an object file
oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.cpp.obj

oatpp/core/async/worker/IOEventWorker_epoll.i: oatpp/core/async/worker/IOEventWorker_epoll.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.i

# target to preprocess a source file
oatpp/core/async/worker/IOEventWorker_epoll.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.cpp.i

oatpp/core/async/worker/IOEventWorker_epoll.s: oatpp/core/async/worker/IOEventWorker_epoll.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.s

# target to generate assembly for a file
oatpp/core/async/worker/IOEventWorker_epoll.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_epoll.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_epoll.cpp.s

oatpp/core/async/worker/IOEventWorker_kqueue.obj: oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.obj

# target to build an object file
oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.cpp.obj

oatpp/core/async/worker/IOEventWorker_kqueue.i: oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.i

# target to preprocess a source file
oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.cpp.i

oatpp/core/async/worker/IOEventWorker_kqueue.s: oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.s

# target to generate assembly for a file
oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_kqueue.cpp.s

oatpp/core/async/worker/IOEventWorker_stub.obj: oatpp/core/async/worker/IOEventWorker_stub.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.obj

# target to build an object file
oatpp/core/async/worker/IOEventWorker_stub.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.obj
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.cpp.obj

oatpp/core/async/worker/IOEventWorker_stub.i: oatpp/core/async/worker/IOEventWorker_stub.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.i

# target to preprocess a source file
oatpp/core/async/worker/IOEventWorker_stub.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.i
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.cpp.i

oatpp/core/async/worker/IOEventWorker_stub.s: oatpp/core/async/worker/IOEventWorker_stub.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.s

# target to generate assembly for a file
oatpp/core/async/worker/IOEventWorker_stub.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOEventWorker_stub.cpp.s
.PHONY : oatpp/core/async/worker/IOEventWorker_stub.cpp.s

oatpp/core/async/worker/IOWorker.obj: oatpp/core/async/worker/IOWorker.cpp.obj
.PHONY : oatpp/core/async/worker/IOWorker.obj

# target to build an object file
oatpp/core/async/worker/IOWorker.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.obj
.PHONY : oatpp/core/async/worker/IOWorker.cpp.obj

oatpp/core/async/worker/IOWorker.i: oatpp/core/async/worker/IOWorker.cpp.i
.PHONY : oatpp/core/async/worker/IOWorker.i

# target to preprocess a source file
oatpp/core/async/worker/IOWorker.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.i
.PHONY : oatpp/core/async/worker/IOWorker.cpp.i

oatpp/core/async/worker/IOWorker.s: oatpp/core/async/worker/IOWorker.cpp.s
.PHONY : oatpp/core/async/worker/IOWorker.s

# target to generate assembly for a file
oatpp/core/async/worker/IOWorker.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/IOWorker.cpp.s
.PHONY : oatpp/core/async/worker/IOWorker.cpp.s

oatpp/core/async/worker/TimerWorker.obj: oatpp/core/async/worker/TimerWorker.cpp.obj
.PHONY : oatpp/core/async/worker/TimerWorker.obj

# target to build an object file
oatpp/core/async/worker/TimerWorker.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.obj
.PHONY : oatpp/core/async/worker/TimerWorker.cpp.obj

oatpp/core/async/worker/TimerWorker.i: oatpp/core/async/worker/TimerWorker.cpp.i
.PHONY : oatpp/core/async/worker/TimerWorker.i

# target to preprocess a source file
oatpp/core/async/worker/TimerWorker.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.i
.PHONY : oatpp/core/async/worker/TimerWorker.cpp.i

oatpp/core/async/worker/TimerWorker.s: oatpp/core/async/worker/TimerWorker.cpp.s
.PHONY : oatpp/core/async/worker/TimerWorker.s

# target to generate assembly for a file
oatpp/core/async/worker/TimerWorker.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/TimerWorker.cpp.s
.PHONY : oatpp/core/async/worker/TimerWorker.cpp.s

oatpp/core/async/worker/Worker.obj: oatpp/core/async/worker/Worker.cpp.obj
.PHONY : oatpp/core/async/worker/Worker.obj

# target to build an object file
oatpp/core/async/worker/Worker.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.obj
.PHONY : oatpp/core/async/worker/Worker.cpp.obj

oatpp/core/async/worker/Worker.i: oatpp/core/async/worker/Worker.cpp.i
.PHONY : oatpp/core/async/worker/Worker.i

# target to preprocess a source file
oatpp/core/async/worker/Worker.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.i
.PHONY : oatpp/core/async/worker/Worker.cpp.i

oatpp/core/async/worker/Worker.s: oatpp/core/async/worker/Worker.cpp.s
.PHONY : oatpp/core/async/worker/Worker.s

# target to generate assembly for a file
oatpp/core/async/worker/Worker.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/async/worker/Worker.cpp.s
.PHONY : oatpp/core/async/worker/Worker.cpp.s

oatpp/core/base/CommandLineArguments.obj: oatpp/core/base/CommandLineArguments.cpp.obj
.PHONY : oatpp/core/base/CommandLineArguments.obj

# target to build an object file
oatpp/core/base/CommandLineArguments.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.obj
.PHONY : oatpp/core/base/CommandLineArguments.cpp.obj

oatpp/core/base/CommandLineArguments.i: oatpp/core/base/CommandLineArguments.cpp.i
.PHONY : oatpp/core/base/CommandLineArguments.i

# target to preprocess a source file
oatpp/core/base/CommandLineArguments.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.i
.PHONY : oatpp/core/base/CommandLineArguments.cpp.i

oatpp/core/base/CommandLineArguments.s: oatpp/core/base/CommandLineArguments.cpp.s
.PHONY : oatpp/core/base/CommandLineArguments.s

# target to generate assembly for a file
oatpp/core/base/CommandLineArguments.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/CommandLineArguments.cpp.s
.PHONY : oatpp/core/base/CommandLineArguments.cpp.s

oatpp/core/base/Countable.obj: oatpp/core/base/Countable.cpp.obj
.PHONY : oatpp/core/base/Countable.obj

# target to build an object file
oatpp/core/base/Countable.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.obj
.PHONY : oatpp/core/base/Countable.cpp.obj

oatpp/core/base/Countable.i: oatpp/core/base/Countable.cpp.i
.PHONY : oatpp/core/base/Countable.i

# target to preprocess a source file
oatpp/core/base/Countable.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.i
.PHONY : oatpp/core/base/Countable.cpp.i

oatpp/core/base/Countable.s: oatpp/core/base/Countable.cpp.s
.PHONY : oatpp/core/base/Countable.s

# target to generate assembly for a file
oatpp/core/base/Countable.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Countable.cpp.s
.PHONY : oatpp/core/base/Countable.cpp.s

oatpp/core/base/Environment.obj: oatpp/core/base/Environment.cpp.obj
.PHONY : oatpp/core/base/Environment.obj

# target to build an object file
oatpp/core/base/Environment.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.obj
.PHONY : oatpp/core/base/Environment.cpp.obj

oatpp/core/base/Environment.i: oatpp/core/base/Environment.cpp.i
.PHONY : oatpp/core/base/Environment.i

# target to preprocess a source file
oatpp/core/base/Environment.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.i
.PHONY : oatpp/core/base/Environment.cpp.i

oatpp/core/base/Environment.s: oatpp/core/base/Environment.cpp.s
.PHONY : oatpp/core/base/Environment.s

# target to generate assembly for a file
oatpp/core/base/Environment.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/base/Environment.cpp.s
.PHONY : oatpp/core/base/Environment.cpp.s

oatpp/core/concurrency/SpinLock.obj: oatpp/core/concurrency/SpinLock.cpp.obj
.PHONY : oatpp/core/concurrency/SpinLock.obj

# target to build an object file
oatpp/core/concurrency/SpinLock.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.obj
.PHONY : oatpp/core/concurrency/SpinLock.cpp.obj

oatpp/core/concurrency/SpinLock.i: oatpp/core/concurrency/SpinLock.cpp.i
.PHONY : oatpp/core/concurrency/SpinLock.i

# target to preprocess a source file
oatpp/core/concurrency/SpinLock.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.i
.PHONY : oatpp/core/concurrency/SpinLock.cpp.i

oatpp/core/concurrency/SpinLock.s: oatpp/core/concurrency/SpinLock.cpp.s
.PHONY : oatpp/core/concurrency/SpinLock.s

# target to generate assembly for a file
oatpp/core/concurrency/SpinLock.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/SpinLock.cpp.s
.PHONY : oatpp/core/concurrency/SpinLock.cpp.s

oatpp/core/concurrency/Thread.obj: oatpp/core/concurrency/Thread.cpp.obj
.PHONY : oatpp/core/concurrency/Thread.obj

# target to build an object file
oatpp/core/concurrency/Thread.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.obj
.PHONY : oatpp/core/concurrency/Thread.cpp.obj

oatpp/core/concurrency/Thread.i: oatpp/core/concurrency/Thread.cpp.i
.PHONY : oatpp/core/concurrency/Thread.i

# target to preprocess a source file
oatpp/core/concurrency/Thread.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.i
.PHONY : oatpp/core/concurrency/Thread.cpp.i

oatpp/core/concurrency/Thread.s: oatpp/core/concurrency/Thread.cpp.s
.PHONY : oatpp/core/concurrency/Thread.s

# target to generate assembly for a file
oatpp/core/concurrency/Thread.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/concurrency/Thread.cpp.s
.PHONY : oatpp/core/concurrency/Thread.cpp.s

oatpp/core/data/Bundle.obj: oatpp/core/data/Bundle.cpp.obj
.PHONY : oatpp/core/data/Bundle.obj

# target to build an object file
oatpp/core/data/Bundle.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.obj
.PHONY : oatpp/core/data/Bundle.cpp.obj

oatpp/core/data/Bundle.i: oatpp/core/data/Bundle.cpp.i
.PHONY : oatpp/core/data/Bundle.i

# target to preprocess a source file
oatpp/core/data/Bundle.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.i
.PHONY : oatpp/core/data/Bundle.cpp.i

oatpp/core/data/Bundle.s: oatpp/core/data/Bundle.cpp.s
.PHONY : oatpp/core/data/Bundle.s

# target to generate assembly for a file
oatpp/core/data/Bundle.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/Bundle.cpp.s
.PHONY : oatpp/core/data/Bundle.cpp.s

oatpp/core/data/buffer/FIFOBuffer.obj: oatpp/core/data/buffer/FIFOBuffer.cpp.obj
.PHONY : oatpp/core/data/buffer/FIFOBuffer.obj

# target to build an object file
oatpp/core/data/buffer/FIFOBuffer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.obj
.PHONY : oatpp/core/data/buffer/FIFOBuffer.cpp.obj

oatpp/core/data/buffer/FIFOBuffer.i: oatpp/core/data/buffer/FIFOBuffer.cpp.i
.PHONY : oatpp/core/data/buffer/FIFOBuffer.i

# target to preprocess a source file
oatpp/core/data/buffer/FIFOBuffer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.i
.PHONY : oatpp/core/data/buffer/FIFOBuffer.cpp.i

oatpp/core/data/buffer/FIFOBuffer.s: oatpp/core/data/buffer/FIFOBuffer.cpp.s
.PHONY : oatpp/core/data/buffer/FIFOBuffer.s

# target to generate assembly for a file
oatpp/core/data/buffer/FIFOBuffer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/FIFOBuffer.cpp.s
.PHONY : oatpp/core/data/buffer/FIFOBuffer.cpp.s

oatpp/core/data/buffer/IOBuffer.obj: oatpp/core/data/buffer/IOBuffer.cpp.obj
.PHONY : oatpp/core/data/buffer/IOBuffer.obj

# target to build an object file
oatpp/core/data/buffer/IOBuffer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.obj
.PHONY : oatpp/core/data/buffer/IOBuffer.cpp.obj

oatpp/core/data/buffer/IOBuffer.i: oatpp/core/data/buffer/IOBuffer.cpp.i
.PHONY : oatpp/core/data/buffer/IOBuffer.i

# target to preprocess a source file
oatpp/core/data/buffer/IOBuffer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.i
.PHONY : oatpp/core/data/buffer/IOBuffer.cpp.i

oatpp/core/data/buffer/IOBuffer.s: oatpp/core/data/buffer/IOBuffer.cpp.s
.PHONY : oatpp/core/data/buffer/IOBuffer.s

# target to generate assembly for a file
oatpp/core/data/buffer/IOBuffer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/IOBuffer.cpp.s
.PHONY : oatpp/core/data/buffer/IOBuffer.cpp.s

oatpp/core/data/buffer/Processor.obj: oatpp/core/data/buffer/Processor.cpp.obj
.PHONY : oatpp/core/data/buffer/Processor.obj

# target to build an object file
oatpp/core/data/buffer/Processor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.obj
.PHONY : oatpp/core/data/buffer/Processor.cpp.obj

oatpp/core/data/buffer/Processor.i: oatpp/core/data/buffer/Processor.cpp.i
.PHONY : oatpp/core/data/buffer/Processor.i

# target to preprocess a source file
oatpp/core/data/buffer/Processor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.i
.PHONY : oatpp/core/data/buffer/Processor.cpp.i

oatpp/core/data/buffer/Processor.s: oatpp/core/data/buffer/Processor.cpp.s
.PHONY : oatpp/core/data/buffer/Processor.s

# target to generate assembly for a file
oatpp/core/data/buffer/Processor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/buffer/Processor.cpp.s
.PHONY : oatpp/core/data/buffer/Processor.cpp.s

oatpp/core/data/mapping/ObjectMapper.obj: oatpp/core/data/mapping/ObjectMapper.cpp.obj
.PHONY : oatpp/core/data/mapping/ObjectMapper.obj

# target to build an object file
oatpp/core/data/mapping/ObjectMapper.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.obj
.PHONY : oatpp/core/data/mapping/ObjectMapper.cpp.obj

oatpp/core/data/mapping/ObjectMapper.i: oatpp/core/data/mapping/ObjectMapper.cpp.i
.PHONY : oatpp/core/data/mapping/ObjectMapper.i

# target to preprocess a source file
oatpp/core/data/mapping/ObjectMapper.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.i
.PHONY : oatpp/core/data/mapping/ObjectMapper.cpp.i

oatpp/core/data/mapping/ObjectMapper.s: oatpp/core/data/mapping/ObjectMapper.cpp.s
.PHONY : oatpp/core/data/mapping/ObjectMapper.s

# target to generate assembly for a file
oatpp/core/data/mapping/ObjectMapper.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/ObjectMapper.cpp.s
.PHONY : oatpp/core/data/mapping/ObjectMapper.cpp.s

oatpp/core/data/mapping/TypeResolver.obj: oatpp/core/data/mapping/TypeResolver.cpp.obj
.PHONY : oatpp/core/data/mapping/TypeResolver.obj

# target to build an object file
oatpp/core/data/mapping/TypeResolver.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.obj
.PHONY : oatpp/core/data/mapping/TypeResolver.cpp.obj

oatpp/core/data/mapping/TypeResolver.i: oatpp/core/data/mapping/TypeResolver.cpp.i
.PHONY : oatpp/core/data/mapping/TypeResolver.i

# target to preprocess a source file
oatpp/core/data/mapping/TypeResolver.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.i
.PHONY : oatpp/core/data/mapping/TypeResolver.cpp.i

oatpp/core/data/mapping/TypeResolver.s: oatpp/core/data/mapping/TypeResolver.cpp.s
.PHONY : oatpp/core/data/mapping/TypeResolver.s

# target to generate assembly for a file
oatpp/core/data/mapping/TypeResolver.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/TypeResolver.cpp.s
.PHONY : oatpp/core/data/mapping/TypeResolver.cpp.s

oatpp/core/data/mapping/type/Any.obj: oatpp/core/data/mapping/type/Any.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Any.obj

# target to build an object file
oatpp/core/data/mapping/type/Any.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Any.cpp.obj

oatpp/core/data/mapping/type/Any.i: oatpp/core/data/mapping/type/Any.cpp.i
.PHONY : oatpp/core/data/mapping/type/Any.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Any.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.i
.PHONY : oatpp/core/data/mapping/type/Any.cpp.i

oatpp/core/data/mapping/type/Any.s: oatpp/core/data/mapping/type/Any.cpp.s
.PHONY : oatpp/core/data/mapping/type/Any.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Any.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Any.cpp.s
.PHONY : oatpp/core/data/mapping/type/Any.cpp.s

oatpp/core/data/mapping/type/Enum.obj: oatpp/core/data/mapping/type/Enum.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Enum.obj

# target to build an object file
oatpp/core/data/mapping/type/Enum.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Enum.cpp.obj

oatpp/core/data/mapping/type/Enum.i: oatpp/core/data/mapping/type/Enum.cpp.i
.PHONY : oatpp/core/data/mapping/type/Enum.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Enum.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.i
.PHONY : oatpp/core/data/mapping/type/Enum.cpp.i

oatpp/core/data/mapping/type/Enum.s: oatpp/core/data/mapping/type/Enum.cpp.s
.PHONY : oatpp/core/data/mapping/type/Enum.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Enum.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Enum.cpp.s
.PHONY : oatpp/core/data/mapping/type/Enum.cpp.s

oatpp/core/data/mapping/type/List.obj: oatpp/core/data/mapping/type/List.cpp.obj
.PHONY : oatpp/core/data/mapping/type/List.obj

# target to build an object file
oatpp/core/data/mapping/type/List.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.obj
.PHONY : oatpp/core/data/mapping/type/List.cpp.obj

oatpp/core/data/mapping/type/List.i: oatpp/core/data/mapping/type/List.cpp.i
.PHONY : oatpp/core/data/mapping/type/List.i

# target to preprocess a source file
oatpp/core/data/mapping/type/List.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.i
.PHONY : oatpp/core/data/mapping/type/List.cpp.i

oatpp/core/data/mapping/type/List.s: oatpp/core/data/mapping/type/List.cpp.s
.PHONY : oatpp/core/data/mapping/type/List.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/List.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/List.cpp.s
.PHONY : oatpp/core/data/mapping/type/List.cpp.s

oatpp/core/data/mapping/type/Object.obj: oatpp/core/data/mapping/type/Object.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Object.obj

# target to build an object file
oatpp/core/data/mapping/type/Object.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Object.cpp.obj

oatpp/core/data/mapping/type/Object.i: oatpp/core/data/mapping/type/Object.cpp.i
.PHONY : oatpp/core/data/mapping/type/Object.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Object.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.i
.PHONY : oatpp/core/data/mapping/type/Object.cpp.i

oatpp/core/data/mapping/type/Object.s: oatpp/core/data/mapping/type/Object.cpp.s
.PHONY : oatpp/core/data/mapping/type/Object.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Object.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Object.cpp.s
.PHONY : oatpp/core/data/mapping/type/Object.cpp.s

oatpp/core/data/mapping/type/PairList.obj: oatpp/core/data/mapping/type/PairList.cpp.obj
.PHONY : oatpp/core/data/mapping/type/PairList.obj

# target to build an object file
oatpp/core/data/mapping/type/PairList.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.obj
.PHONY : oatpp/core/data/mapping/type/PairList.cpp.obj

oatpp/core/data/mapping/type/PairList.i: oatpp/core/data/mapping/type/PairList.cpp.i
.PHONY : oatpp/core/data/mapping/type/PairList.i

# target to preprocess a source file
oatpp/core/data/mapping/type/PairList.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.i
.PHONY : oatpp/core/data/mapping/type/PairList.cpp.i

oatpp/core/data/mapping/type/PairList.s: oatpp/core/data/mapping/type/PairList.cpp.s
.PHONY : oatpp/core/data/mapping/type/PairList.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/PairList.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/PairList.cpp.s
.PHONY : oatpp/core/data/mapping/type/PairList.cpp.s

oatpp/core/data/mapping/type/Primitive.obj: oatpp/core/data/mapping/type/Primitive.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Primitive.obj

# target to build an object file
oatpp/core/data/mapping/type/Primitive.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Primitive.cpp.obj

oatpp/core/data/mapping/type/Primitive.i: oatpp/core/data/mapping/type/Primitive.cpp.i
.PHONY : oatpp/core/data/mapping/type/Primitive.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Primitive.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.i
.PHONY : oatpp/core/data/mapping/type/Primitive.cpp.i

oatpp/core/data/mapping/type/Primitive.s: oatpp/core/data/mapping/type/Primitive.cpp.s
.PHONY : oatpp/core/data/mapping/type/Primitive.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Primitive.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Primitive.cpp.s
.PHONY : oatpp/core/data/mapping/type/Primitive.cpp.s

oatpp/core/data/mapping/type/Type.obj: oatpp/core/data/mapping/type/Type.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Type.obj

# target to build an object file
oatpp/core/data/mapping/type/Type.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Type.cpp.obj

oatpp/core/data/mapping/type/Type.i: oatpp/core/data/mapping/type/Type.cpp.i
.PHONY : oatpp/core/data/mapping/type/Type.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Type.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.i
.PHONY : oatpp/core/data/mapping/type/Type.cpp.i

oatpp/core/data/mapping/type/Type.s: oatpp/core/data/mapping/type/Type.cpp.s
.PHONY : oatpp/core/data/mapping/type/Type.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Type.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Type.cpp.s
.PHONY : oatpp/core/data/mapping/type/Type.cpp.s

oatpp/core/data/mapping/type/UnorderedMap.obj: oatpp/core/data/mapping/type/UnorderedMap.cpp.obj
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.obj

# target to build an object file
oatpp/core/data/mapping/type/UnorderedMap.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.obj
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.cpp.obj

oatpp/core/data/mapping/type/UnorderedMap.i: oatpp/core/data/mapping/type/UnorderedMap.cpp.i
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.i

# target to preprocess a source file
oatpp/core/data/mapping/type/UnorderedMap.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.i
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.cpp.i

oatpp/core/data/mapping/type/UnorderedMap.s: oatpp/core/data/mapping/type/UnorderedMap.cpp.s
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/UnorderedMap.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedMap.cpp.s
.PHONY : oatpp/core/data/mapping/type/UnorderedMap.cpp.s

oatpp/core/data/mapping/type/UnorderedSet.obj: oatpp/core/data/mapping/type/UnorderedSet.cpp.obj
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.obj

# target to build an object file
oatpp/core/data/mapping/type/UnorderedSet.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.obj
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.cpp.obj

oatpp/core/data/mapping/type/UnorderedSet.i: oatpp/core/data/mapping/type/UnorderedSet.cpp.i
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.i

# target to preprocess a source file
oatpp/core/data/mapping/type/UnorderedSet.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.i
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.cpp.i

oatpp/core/data/mapping/type/UnorderedSet.s: oatpp/core/data/mapping/type/UnorderedSet.cpp.s
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/UnorderedSet.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/UnorderedSet.cpp.s
.PHONY : oatpp/core/data/mapping/type/UnorderedSet.cpp.s

oatpp/core/data/mapping/type/Vector.obj: oatpp/core/data/mapping/type/Vector.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Vector.obj

# target to build an object file
oatpp/core/data/mapping/type/Vector.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.obj
.PHONY : oatpp/core/data/mapping/type/Vector.cpp.obj

oatpp/core/data/mapping/type/Vector.i: oatpp/core/data/mapping/type/Vector.cpp.i
.PHONY : oatpp/core/data/mapping/type/Vector.i

# target to preprocess a source file
oatpp/core/data/mapping/type/Vector.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.i
.PHONY : oatpp/core/data/mapping/type/Vector.cpp.i

oatpp/core/data/mapping/type/Vector.s: oatpp/core/data/mapping/type/Vector.cpp.s
.PHONY : oatpp/core/data/mapping/type/Vector.s

# target to generate assembly for a file
oatpp/core/data/mapping/type/Vector.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/mapping/type/Vector.cpp.s
.PHONY : oatpp/core/data/mapping/type/Vector.cpp.s

oatpp/core/data/resource/File.obj: oatpp/core/data/resource/File.cpp.obj
.PHONY : oatpp/core/data/resource/File.obj

# target to build an object file
oatpp/core/data/resource/File.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.obj
.PHONY : oatpp/core/data/resource/File.cpp.obj

oatpp/core/data/resource/File.i: oatpp/core/data/resource/File.cpp.i
.PHONY : oatpp/core/data/resource/File.i

# target to preprocess a source file
oatpp/core/data/resource/File.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.i
.PHONY : oatpp/core/data/resource/File.cpp.i

oatpp/core/data/resource/File.s: oatpp/core/data/resource/File.cpp.s
.PHONY : oatpp/core/data/resource/File.s

# target to generate assembly for a file
oatpp/core/data/resource/File.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/File.cpp.s
.PHONY : oatpp/core/data/resource/File.cpp.s

oatpp/core/data/resource/InMemoryData.obj: oatpp/core/data/resource/InMemoryData.cpp.obj
.PHONY : oatpp/core/data/resource/InMemoryData.obj

# target to build an object file
oatpp/core/data/resource/InMemoryData.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.obj
.PHONY : oatpp/core/data/resource/InMemoryData.cpp.obj

oatpp/core/data/resource/InMemoryData.i: oatpp/core/data/resource/InMemoryData.cpp.i
.PHONY : oatpp/core/data/resource/InMemoryData.i

# target to preprocess a source file
oatpp/core/data/resource/InMemoryData.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.i
.PHONY : oatpp/core/data/resource/InMemoryData.cpp.i

oatpp/core/data/resource/InMemoryData.s: oatpp/core/data/resource/InMemoryData.cpp.s
.PHONY : oatpp/core/data/resource/InMemoryData.s

# target to generate assembly for a file
oatpp/core/data/resource/InMemoryData.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/InMemoryData.cpp.s
.PHONY : oatpp/core/data/resource/InMemoryData.cpp.s

oatpp/core/data/resource/TemporaryFile.obj: oatpp/core/data/resource/TemporaryFile.cpp.obj
.PHONY : oatpp/core/data/resource/TemporaryFile.obj

# target to build an object file
oatpp/core/data/resource/TemporaryFile.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.obj
.PHONY : oatpp/core/data/resource/TemporaryFile.cpp.obj

oatpp/core/data/resource/TemporaryFile.i: oatpp/core/data/resource/TemporaryFile.cpp.i
.PHONY : oatpp/core/data/resource/TemporaryFile.i

# target to preprocess a source file
oatpp/core/data/resource/TemporaryFile.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.i
.PHONY : oatpp/core/data/resource/TemporaryFile.cpp.i

oatpp/core/data/resource/TemporaryFile.s: oatpp/core/data/resource/TemporaryFile.cpp.s
.PHONY : oatpp/core/data/resource/TemporaryFile.s

# target to generate assembly for a file
oatpp/core/data/resource/TemporaryFile.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/resource/TemporaryFile.cpp.s
.PHONY : oatpp/core/data/resource/TemporaryFile.cpp.s

oatpp/core/data/share/MemoryLabel.obj: oatpp/core/data/share/MemoryLabel.cpp.obj
.PHONY : oatpp/core/data/share/MemoryLabel.obj

# target to build an object file
oatpp/core/data/share/MemoryLabel.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.obj
.PHONY : oatpp/core/data/share/MemoryLabel.cpp.obj

oatpp/core/data/share/MemoryLabel.i: oatpp/core/data/share/MemoryLabel.cpp.i
.PHONY : oatpp/core/data/share/MemoryLabel.i

# target to preprocess a source file
oatpp/core/data/share/MemoryLabel.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.i
.PHONY : oatpp/core/data/share/MemoryLabel.cpp.i

oatpp/core/data/share/MemoryLabel.s: oatpp/core/data/share/MemoryLabel.cpp.s
.PHONY : oatpp/core/data/share/MemoryLabel.s

# target to generate assembly for a file
oatpp/core/data/share/MemoryLabel.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/MemoryLabel.cpp.s
.PHONY : oatpp/core/data/share/MemoryLabel.cpp.s

oatpp/core/data/share/StringTemplate.obj: oatpp/core/data/share/StringTemplate.cpp.obj
.PHONY : oatpp/core/data/share/StringTemplate.obj

# target to build an object file
oatpp/core/data/share/StringTemplate.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.obj
.PHONY : oatpp/core/data/share/StringTemplate.cpp.obj

oatpp/core/data/share/StringTemplate.i: oatpp/core/data/share/StringTemplate.cpp.i
.PHONY : oatpp/core/data/share/StringTemplate.i

# target to preprocess a source file
oatpp/core/data/share/StringTemplate.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.i
.PHONY : oatpp/core/data/share/StringTemplate.cpp.i

oatpp/core/data/share/StringTemplate.s: oatpp/core/data/share/StringTemplate.cpp.s
.PHONY : oatpp/core/data/share/StringTemplate.s

# target to generate assembly for a file
oatpp/core/data/share/StringTemplate.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/share/StringTemplate.cpp.s
.PHONY : oatpp/core/data/share/StringTemplate.cpp.s

oatpp/core/data/stream/BufferStream.obj: oatpp/core/data/stream/BufferStream.cpp.obj
.PHONY : oatpp/core/data/stream/BufferStream.obj

# target to build an object file
oatpp/core/data/stream/BufferStream.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.obj
.PHONY : oatpp/core/data/stream/BufferStream.cpp.obj

oatpp/core/data/stream/BufferStream.i: oatpp/core/data/stream/BufferStream.cpp.i
.PHONY : oatpp/core/data/stream/BufferStream.i

# target to preprocess a source file
oatpp/core/data/stream/BufferStream.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.i
.PHONY : oatpp/core/data/stream/BufferStream.cpp.i

oatpp/core/data/stream/BufferStream.s: oatpp/core/data/stream/BufferStream.cpp.s
.PHONY : oatpp/core/data/stream/BufferStream.s

# target to generate assembly for a file
oatpp/core/data/stream/BufferStream.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/BufferStream.cpp.s
.PHONY : oatpp/core/data/stream/BufferStream.cpp.s

oatpp/core/data/stream/FIFOStream.obj: oatpp/core/data/stream/FIFOStream.cpp.obj
.PHONY : oatpp/core/data/stream/FIFOStream.obj

# target to build an object file
oatpp/core/data/stream/FIFOStream.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.obj
.PHONY : oatpp/core/data/stream/FIFOStream.cpp.obj

oatpp/core/data/stream/FIFOStream.i: oatpp/core/data/stream/FIFOStream.cpp.i
.PHONY : oatpp/core/data/stream/FIFOStream.i

# target to preprocess a source file
oatpp/core/data/stream/FIFOStream.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.i
.PHONY : oatpp/core/data/stream/FIFOStream.cpp.i

oatpp/core/data/stream/FIFOStream.s: oatpp/core/data/stream/FIFOStream.cpp.s
.PHONY : oatpp/core/data/stream/FIFOStream.s

# target to generate assembly for a file
oatpp/core/data/stream/FIFOStream.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FIFOStream.cpp.s
.PHONY : oatpp/core/data/stream/FIFOStream.cpp.s

oatpp/core/data/stream/FileStream.obj: oatpp/core/data/stream/FileStream.cpp.obj
.PHONY : oatpp/core/data/stream/FileStream.obj

# target to build an object file
oatpp/core/data/stream/FileStream.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.obj
.PHONY : oatpp/core/data/stream/FileStream.cpp.obj

oatpp/core/data/stream/FileStream.i: oatpp/core/data/stream/FileStream.cpp.i
.PHONY : oatpp/core/data/stream/FileStream.i

# target to preprocess a source file
oatpp/core/data/stream/FileStream.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.i
.PHONY : oatpp/core/data/stream/FileStream.cpp.i

oatpp/core/data/stream/FileStream.s: oatpp/core/data/stream/FileStream.cpp.s
.PHONY : oatpp/core/data/stream/FileStream.s

# target to generate assembly for a file
oatpp/core/data/stream/FileStream.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/FileStream.cpp.s
.PHONY : oatpp/core/data/stream/FileStream.cpp.s

oatpp/core/data/stream/Stream.obj: oatpp/core/data/stream/Stream.cpp.obj
.PHONY : oatpp/core/data/stream/Stream.obj

# target to build an object file
oatpp/core/data/stream/Stream.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.obj
.PHONY : oatpp/core/data/stream/Stream.cpp.obj

oatpp/core/data/stream/Stream.i: oatpp/core/data/stream/Stream.cpp.i
.PHONY : oatpp/core/data/stream/Stream.i

# target to preprocess a source file
oatpp/core/data/stream/Stream.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.i
.PHONY : oatpp/core/data/stream/Stream.cpp.i

oatpp/core/data/stream/Stream.s: oatpp/core/data/stream/Stream.cpp.s
.PHONY : oatpp/core/data/stream/Stream.s

# target to generate assembly for a file
oatpp/core/data/stream/Stream.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/Stream.cpp.s
.PHONY : oatpp/core/data/stream/Stream.cpp.s

oatpp/core/data/stream/StreamBufferedProxy.obj: oatpp/core/data/stream/StreamBufferedProxy.cpp.obj
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.obj

# target to build an object file
oatpp/core/data/stream/StreamBufferedProxy.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.obj
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.cpp.obj

oatpp/core/data/stream/StreamBufferedProxy.i: oatpp/core/data/stream/StreamBufferedProxy.cpp.i
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.i

# target to preprocess a source file
oatpp/core/data/stream/StreamBufferedProxy.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.i
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.cpp.i

oatpp/core/data/stream/StreamBufferedProxy.s: oatpp/core/data/stream/StreamBufferedProxy.cpp.s
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.s

# target to generate assembly for a file
oatpp/core/data/stream/StreamBufferedProxy.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/data/stream/StreamBufferedProxy.cpp.s
.PHONY : oatpp/core/data/stream/StreamBufferedProxy.cpp.s

oatpp/core/parser/Caret.obj: oatpp/core/parser/Caret.cpp.obj
.PHONY : oatpp/core/parser/Caret.obj

# target to build an object file
oatpp/core/parser/Caret.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.obj
.PHONY : oatpp/core/parser/Caret.cpp.obj

oatpp/core/parser/Caret.i: oatpp/core/parser/Caret.cpp.i
.PHONY : oatpp/core/parser/Caret.i

# target to preprocess a source file
oatpp/core/parser/Caret.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.i
.PHONY : oatpp/core/parser/Caret.cpp.i

oatpp/core/parser/Caret.s: oatpp/core/parser/Caret.cpp.s
.PHONY : oatpp/core/parser/Caret.s

# target to generate assembly for a file
oatpp/core/parser/Caret.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/Caret.cpp.s
.PHONY : oatpp/core/parser/Caret.cpp.s

oatpp/core/parser/ParsingError.obj: oatpp/core/parser/ParsingError.cpp.obj
.PHONY : oatpp/core/parser/ParsingError.obj

# target to build an object file
oatpp/core/parser/ParsingError.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.obj
.PHONY : oatpp/core/parser/ParsingError.cpp.obj

oatpp/core/parser/ParsingError.i: oatpp/core/parser/ParsingError.cpp.i
.PHONY : oatpp/core/parser/ParsingError.i

# target to preprocess a source file
oatpp/core/parser/ParsingError.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.i
.PHONY : oatpp/core/parser/ParsingError.cpp.i

oatpp/core/parser/ParsingError.s: oatpp/core/parser/ParsingError.cpp.s
.PHONY : oatpp/core/parser/ParsingError.s

# target to generate assembly for a file
oatpp/core/parser/ParsingError.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/parser/ParsingError.cpp.s
.PHONY : oatpp/core/parser/ParsingError.cpp.s

oatpp/core/utils/Binary.obj: oatpp/core/utils/Binary.cpp.obj
.PHONY : oatpp/core/utils/Binary.obj

# target to build an object file
oatpp/core/utils/Binary.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.obj
.PHONY : oatpp/core/utils/Binary.cpp.obj

oatpp/core/utils/Binary.i: oatpp/core/utils/Binary.cpp.i
.PHONY : oatpp/core/utils/Binary.i

# target to preprocess a source file
oatpp/core/utils/Binary.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.i
.PHONY : oatpp/core/utils/Binary.cpp.i

oatpp/core/utils/Binary.s: oatpp/core/utils/Binary.cpp.s
.PHONY : oatpp/core/utils/Binary.s

# target to generate assembly for a file
oatpp/core/utils/Binary.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Binary.cpp.s
.PHONY : oatpp/core/utils/Binary.cpp.s

oatpp/core/utils/ConversionUtils.obj: oatpp/core/utils/ConversionUtils.cpp.obj
.PHONY : oatpp/core/utils/ConversionUtils.obj

# target to build an object file
oatpp/core/utils/ConversionUtils.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.obj
.PHONY : oatpp/core/utils/ConversionUtils.cpp.obj

oatpp/core/utils/ConversionUtils.i: oatpp/core/utils/ConversionUtils.cpp.i
.PHONY : oatpp/core/utils/ConversionUtils.i

# target to preprocess a source file
oatpp/core/utils/ConversionUtils.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.i
.PHONY : oatpp/core/utils/ConversionUtils.cpp.i

oatpp/core/utils/ConversionUtils.s: oatpp/core/utils/ConversionUtils.cpp.s
.PHONY : oatpp/core/utils/ConversionUtils.s

# target to generate assembly for a file
oatpp/core/utils/ConversionUtils.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/ConversionUtils.cpp.s
.PHONY : oatpp/core/utils/ConversionUtils.cpp.s

oatpp/core/utils/Random.obj: oatpp/core/utils/Random.cpp.obj
.PHONY : oatpp/core/utils/Random.obj

# target to build an object file
oatpp/core/utils/Random.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.obj
.PHONY : oatpp/core/utils/Random.cpp.obj

oatpp/core/utils/Random.i: oatpp/core/utils/Random.cpp.i
.PHONY : oatpp/core/utils/Random.i

# target to preprocess a source file
oatpp/core/utils/Random.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.i
.PHONY : oatpp/core/utils/Random.cpp.i

oatpp/core/utils/Random.s: oatpp/core/utils/Random.cpp.s
.PHONY : oatpp/core/utils/Random.s

# target to generate assembly for a file
oatpp/core/utils/Random.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/Random.cpp.s
.PHONY : oatpp/core/utils/Random.cpp.s

oatpp/core/utils/String.obj: oatpp/core/utils/String.cpp.obj
.PHONY : oatpp/core/utils/String.obj

# target to build an object file
oatpp/core/utils/String.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.obj
.PHONY : oatpp/core/utils/String.cpp.obj

oatpp/core/utils/String.i: oatpp/core/utils/String.cpp.i
.PHONY : oatpp/core/utils/String.i

# target to preprocess a source file
oatpp/core/utils/String.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.i
.PHONY : oatpp/core/utils/String.cpp.i

oatpp/core/utils/String.s: oatpp/core/utils/String.cpp.s
.PHONY : oatpp/core/utils/String.s

# target to generate assembly for a file
oatpp/core/utils/String.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/core/utils/String.cpp.s
.PHONY : oatpp/core/utils/String.cpp.s

oatpp/encoding/Base64.obj: oatpp/encoding/Base64.cpp.obj
.PHONY : oatpp/encoding/Base64.obj

# target to build an object file
oatpp/encoding/Base64.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.obj
.PHONY : oatpp/encoding/Base64.cpp.obj

oatpp/encoding/Base64.i: oatpp/encoding/Base64.cpp.i
.PHONY : oatpp/encoding/Base64.i

# target to preprocess a source file
oatpp/encoding/Base64.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.i
.PHONY : oatpp/encoding/Base64.cpp.i

oatpp/encoding/Base64.s: oatpp/encoding/Base64.cpp.s
.PHONY : oatpp/encoding/Base64.s

# target to generate assembly for a file
oatpp/encoding/Base64.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Base64.cpp.s
.PHONY : oatpp/encoding/Base64.cpp.s

oatpp/encoding/Hex.obj: oatpp/encoding/Hex.cpp.obj
.PHONY : oatpp/encoding/Hex.obj

# target to build an object file
oatpp/encoding/Hex.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.obj
.PHONY : oatpp/encoding/Hex.cpp.obj

oatpp/encoding/Hex.i: oatpp/encoding/Hex.cpp.i
.PHONY : oatpp/encoding/Hex.i

# target to preprocess a source file
oatpp/encoding/Hex.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.i
.PHONY : oatpp/encoding/Hex.cpp.i

oatpp/encoding/Hex.s: oatpp/encoding/Hex.cpp.s
.PHONY : oatpp/encoding/Hex.s

# target to generate assembly for a file
oatpp/encoding/Hex.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Hex.cpp.s
.PHONY : oatpp/encoding/Hex.cpp.s

oatpp/encoding/Unicode.obj: oatpp/encoding/Unicode.cpp.obj
.PHONY : oatpp/encoding/Unicode.obj

# target to build an object file
oatpp/encoding/Unicode.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.obj
.PHONY : oatpp/encoding/Unicode.cpp.obj

oatpp/encoding/Unicode.i: oatpp/encoding/Unicode.cpp.i
.PHONY : oatpp/encoding/Unicode.i

# target to preprocess a source file
oatpp/encoding/Unicode.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.i
.PHONY : oatpp/encoding/Unicode.cpp.i

oatpp/encoding/Unicode.s: oatpp/encoding/Unicode.cpp.s
.PHONY : oatpp/encoding/Unicode.s

# target to generate assembly for a file
oatpp/encoding/Unicode.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Unicode.cpp.s
.PHONY : oatpp/encoding/Unicode.cpp.s

oatpp/encoding/Url.obj: oatpp/encoding/Url.cpp.obj
.PHONY : oatpp/encoding/Url.obj

# target to build an object file
oatpp/encoding/Url.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.obj
.PHONY : oatpp/encoding/Url.cpp.obj

oatpp/encoding/Url.i: oatpp/encoding/Url.cpp.i
.PHONY : oatpp/encoding/Url.i

# target to preprocess a source file
oatpp/encoding/Url.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.i
.PHONY : oatpp/encoding/Url.cpp.i

oatpp/encoding/Url.s: oatpp/encoding/Url.cpp.s
.PHONY : oatpp/encoding/Url.s

# target to generate assembly for a file
oatpp/encoding/Url.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/encoding/Url.cpp.s
.PHONY : oatpp/encoding/Url.cpp.s

oatpp/network/Address.obj: oatpp/network/Address.cpp.obj
.PHONY : oatpp/network/Address.obj

# target to build an object file
oatpp/network/Address.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.obj
.PHONY : oatpp/network/Address.cpp.obj

oatpp/network/Address.i: oatpp/network/Address.cpp.i
.PHONY : oatpp/network/Address.i

# target to preprocess a source file
oatpp/network/Address.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.i
.PHONY : oatpp/network/Address.cpp.i

oatpp/network/Address.s: oatpp/network/Address.cpp.s
.PHONY : oatpp/network/Address.s

# target to generate assembly for a file
oatpp/network/Address.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Address.cpp.s
.PHONY : oatpp/network/Address.cpp.s

oatpp/network/ConnectionPool.obj: oatpp/network/ConnectionPool.cpp.obj
.PHONY : oatpp/network/ConnectionPool.obj

# target to build an object file
oatpp/network/ConnectionPool.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.obj
.PHONY : oatpp/network/ConnectionPool.cpp.obj

oatpp/network/ConnectionPool.i: oatpp/network/ConnectionPool.cpp.i
.PHONY : oatpp/network/ConnectionPool.i

# target to preprocess a source file
oatpp/network/ConnectionPool.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.i
.PHONY : oatpp/network/ConnectionPool.cpp.i

oatpp/network/ConnectionPool.s: oatpp/network/ConnectionPool.cpp.s
.PHONY : oatpp/network/ConnectionPool.s

# target to generate assembly for a file
oatpp/network/ConnectionPool.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionPool.cpp.s
.PHONY : oatpp/network/ConnectionPool.cpp.s

oatpp/network/ConnectionProvider.obj: oatpp/network/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/ConnectionProvider.obj

# target to build an object file
oatpp/network/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/ConnectionProvider.cpp.obj

oatpp/network/ConnectionProvider.i: oatpp/network/ConnectionProvider.cpp.i
.PHONY : oatpp/network/ConnectionProvider.i

# target to preprocess a source file
oatpp/network/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.i
.PHONY : oatpp/network/ConnectionProvider.cpp.i

oatpp/network/ConnectionProvider.s: oatpp/network/ConnectionProvider.cpp.s
.PHONY : oatpp/network/ConnectionProvider.s

# target to generate assembly for a file
oatpp/network/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProvider.cpp.s
.PHONY : oatpp/network/ConnectionProvider.cpp.s

oatpp/network/ConnectionProviderSwitch.obj: oatpp/network/ConnectionProviderSwitch.cpp.obj
.PHONY : oatpp/network/ConnectionProviderSwitch.obj

# target to build an object file
oatpp/network/ConnectionProviderSwitch.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.obj
.PHONY : oatpp/network/ConnectionProviderSwitch.cpp.obj

oatpp/network/ConnectionProviderSwitch.i: oatpp/network/ConnectionProviderSwitch.cpp.i
.PHONY : oatpp/network/ConnectionProviderSwitch.i

# target to preprocess a source file
oatpp/network/ConnectionProviderSwitch.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.i
.PHONY : oatpp/network/ConnectionProviderSwitch.cpp.i

oatpp/network/ConnectionProviderSwitch.s: oatpp/network/ConnectionProviderSwitch.cpp.s
.PHONY : oatpp/network/ConnectionProviderSwitch.s

# target to generate assembly for a file
oatpp/network/ConnectionProviderSwitch.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/ConnectionProviderSwitch.cpp.s
.PHONY : oatpp/network/ConnectionProviderSwitch.cpp.s

oatpp/network/Server.obj: oatpp/network/Server.cpp.obj
.PHONY : oatpp/network/Server.obj

# target to build an object file
oatpp/network/Server.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.obj
.PHONY : oatpp/network/Server.cpp.obj

oatpp/network/Server.i: oatpp/network/Server.cpp.i
.PHONY : oatpp/network/Server.i

# target to preprocess a source file
oatpp/network/Server.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.i
.PHONY : oatpp/network/Server.cpp.i

oatpp/network/Server.s: oatpp/network/Server.cpp.s
.PHONY : oatpp/network/Server.s

# target to generate assembly for a file
oatpp/network/Server.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Server.cpp.s
.PHONY : oatpp/network/Server.cpp.s

oatpp/network/Url.obj: oatpp/network/Url.cpp.obj
.PHONY : oatpp/network/Url.obj

# target to build an object file
oatpp/network/Url.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.obj
.PHONY : oatpp/network/Url.cpp.obj

oatpp/network/Url.i: oatpp/network/Url.cpp.i
.PHONY : oatpp/network/Url.i

# target to preprocess a source file
oatpp/network/Url.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.i
.PHONY : oatpp/network/Url.cpp.i

oatpp/network/Url.s: oatpp/network/Url.cpp.s
.PHONY : oatpp/network/Url.s

# target to generate assembly for a file
oatpp/network/Url.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/Url.cpp.s
.PHONY : oatpp/network/Url.cpp.s

oatpp/network/monitor/ConnectionInactivityChecker.obj: oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.obj

# target to build an object file
oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.cpp.obj

oatpp/network/monitor/ConnectionInactivityChecker.i: oatpp/network/monitor/ConnectionInactivityChecker.cpp.i
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.i

# target to preprocess a source file
oatpp/network/monitor/ConnectionInactivityChecker.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.i
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.cpp.i

oatpp/network/monitor/ConnectionInactivityChecker.s: oatpp/network/monitor/ConnectionInactivityChecker.cpp.s
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.s

# target to generate assembly for a file
oatpp/network/monitor/ConnectionInactivityChecker.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionInactivityChecker.cpp.s
.PHONY : oatpp/network/monitor/ConnectionInactivityChecker.cpp.s

oatpp/network/monitor/ConnectionMaxAgeChecker.obj: oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.obj

# target to build an object file
oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.obj

oatpp/network/monitor/ConnectionMaxAgeChecker.i: oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.i

# target to preprocess a source file
oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.i

oatpp/network/monitor/ConnectionMaxAgeChecker.s: oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.s

# target to generate assembly for a file
oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s
.PHONY : oatpp/network/monitor/ConnectionMaxAgeChecker.cpp.s

oatpp/network/monitor/ConnectionMonitor.obj: oatpp/network/monitor/ConnectionMonitor.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionMonitor.obj

# target to build an object file
oatpp/network/monitor/ConnectionMonitor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.obj
.PHONY : oatpp/network/monitor/ConnectionMonitor.cpp.obj

oatpp/network/monitor/ConnectionMonitor.i: oatpp/network/monitor/ConnectionMonitor.cpp.i
.PHONY : oatpp/network/monitor/ConnectionMonitor.i

# target to preprocess a source file
oatpp/network/monitor/ConnectionMonitor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.i
.PHONY : oatpp/network/monitor/ConnectionMonitor.cpp.i

oatpp/network/monitor/ConnectionMonitor.s: oatpp/network/monitor/ConnectionMonitor.cpp.s
.PHONY : oatpp/network/monitor/ConnectionMonitor.s

# target to generate assembly for a file
oatpp/network/monitor/ConnectionMonitor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/monitor/ConnectionMonitor.cpp.s
.PHONY : oatpp/network/monitor/ConnectionMonitor.cpp.s

oatpp/network/tcp/Connection.obj: oatpp/network/tcp/Connection.cpp.obj
.PHONY : oatpp/network/tcp/Connection.obj

# target to build an object file
oatpp/network/tcp/Connection.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.obj
.PHONY : oatpp/network/tcp/Connection.cpp.obj

oatpp/network/tcp/Connection.i: oatpp/network/tcp/Connection.cpp.i
.PHONY : oatpp/network/tcp/Connection.i

# target to preprocess a source file
oatpp/network/tcp/Connection.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.i
.PHONY : oatpp/network/tcp/Connection.cpp.i

oatpp/network/tcp/Connection.s: oatpp/network/tcp/Connection.cpp.s
.PHONY : oatpp/network/tcp/Connection.s

# target to generate assembly for a file
oatpp/network/tcp/Connection.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/Connection.cpp.s
.PHONY : oatpp/network/tcp/Connection.cpp.s

oatpp/network/tcp/client/ConnectionProvider.obj: oatpp/network/tcp/client/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/tcp/client/ConnectionProvider.obj

# target to build an object file
oatpp/network/tcp/client/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/tcp/client/ConnectionProvider.cpp.obj

oatpp/network/tcp/client/ConnectionProvider.i: oatpp/network/tcp/client/ConnectionProvider.cpp.i
.PHONY : oatpp/network/tcp/client/ConnectionProvider.i

# target to preprocess a source file
oatpp/network/tcp/client/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.i
.PHONY : oatpp/network/tcp/client/ConnectionProvider.cpp.i

oatpp/network/tcp/client/ConnectionProvider.s: oatpp/network/tcp/client/ConnectionProvider.cpp.s
.PHONY : oatpp/network/tcp/client/ConnectionProvider.s

# target to generate assembly for a file
oatpp/network/tcp/client/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/client/ConnectionProvider.cpp.s
.PHONY : oatpp/network/tcp/client/ConnectionProvider.cpp.s

oatpp/network/tcp/server/ConnectionProvider.obj: oatpp/network/tcp/server/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/tcp/server/ConnectionProvider.obj

# target to build an object file
oatpp/network/tcp/server/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/tcp/server/ConnectionProvider.cpp.obj

oatpp/network/tcp/server/ConnectionProvider.i: oatpp/network/tcp/server/ConnectionProvider.cpp.i
.PHONY : oatpp/network/tcp/server/ConnectionProvider.i

# target to preprocess a source file
oatpp/network/tcp/server/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.i
.PHONY : oatpp/network/tcp/server/ConnectionProvider.cpp.i

oatpp/network/tcp/server/ConnectionProvider.s: oatpp/network/tcp/server/ConnectionProvider.cpp.s
.PHONY : oatpp/network/tcp/server/ConnectionProvider.s

# target to generate assembly for a file
oatpp/network/tcp/server/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/tcp/server/ConnectionProvider.cpp.s
.PHONY : oatpp/network/tcp/server/ConnectionProvider.cpp.s

oatpp/network/virtual_/Interface.obj: oatpp/network/virtual_/Interface.cpp.obj
.PHONY : oatpp/network/virtual_/Interface.obj

# target to build an object file
oatpp/network/virtual_/Interface.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.obj
.PHONY : oatpp/network/virtual_/Interface.cpp.obj

oatpp/network/virtual_/Interface.i: oatpp/network/virtual_/Interface.cpp.i
.PHONY : oatpp/network/virtual_/Interface.i

# target to preprocess a source file
oatpp/network/virtual_/Interface.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.i
.PHONY : oatpp/network/virtual_/Interface.cpp.i

oatpp/network/virtual_/Interface.s: oatpp/network/virtual_/Interface.cpp.s
.PHONY : oatpp/network/virtual_/Interface.s

# target to generate assembly for a file
oatpp/network/virtual_/Interface.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Interface.cpp.s
.PHONY : oatpp/network/virtual_/Interface.cpp.s

oatpp/network/virtual_/Pipe.obj: oatpp/network/virtual_/Pipe.cpp.obj
.PHONY : oatpp/network/virtual_/Pipe.obj

# target to build an object file
oatpp/network/virtual_/Pipe.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.obj
.PHONY : oatpp/network/virtual_/Pipe.cpp.obj

oatpp/network/virtual_/Pipe.i: oatpp/network/virtual_/Pipe.cpp.i
.PHONY : oatpp/network/virtual_/Pipe.i

# target to preprocess a source file
oatpp/network/virtual_/Pipe.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.i
.PHONY : oatpp/network/virtual_/Pipe.cpp.i

oatpp/network/virtual_/Pipe.s: oatpp/network/virtual_/Pipe.cpp.s
.PHONY : oatpp/network/virtual_/Pipe.s

# target to generate assembly for a file
oatpp/network/virtual_/Pipe.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Pipe.cpp.s
.PHONY : oatpp/network/virtual_/Pipe.cpp.s

oatpp/network/virtual_/Socket.obj: oatpp/network/virtual_/Socket.cpp.obj
.PHONY : oatpp/network/virtual_/Socket.obj

# target to build an object file
oatpp/network/virtual_/Socket.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.obj
.PHONY : oatpp/network/virtual_/Socket.cpp.obj

oatpp/network/virtual_/Socket.i: oatpp/network/virtual_/Socket.cpp.i
.PHONY : oatpp/network/virtual_/Socket.i

# target to preprocess a source file
oatpp/network/virtual_/Socket.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.i
.PHONY : oatpp/network/virtual_/Socket.cpp.i

oatpp/network/virtual_/Socket.s: oatpp/network/virtual_/Socket.cpp.s
.PHONY : oatpp/network/virtual_/Socket.s

# target to generate assembly for a file
oatpp/network/virtual_/Socket.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/Socket.cpp.s
.PHONY : oatpp/network/virtual_/Socket.cpp.s

oatpp/network/virtual_/client/ConnectionProvider.obj: oatpp/network/virtual_/client/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.obj

# target to build an object file
oatpp/network/virtual_/client/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.cpp.obj

oatpp/network/virtual_/client/ConnectionProvider.i: oatpp/network/virtual_/client/ConnectionProvider.cpp.i
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.i

# target to preprocess a source file
oatpp/network/virtual_/client/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.i
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.cpp.i

oatpp/network/virtual_/client/ConnectionProvider.s: oatpp/network/virtual_/client/ConnectionProvider.cpp.s
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.s

# target to generate assembly for a file
oatpp/network/virtual_/client/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/client/ConnectionProvider.cpp.s
.PHONY : oatpp/network/virtual_/client/ConnectionProvider.cpp.s

oatpp/network/virtual_/server/ConnectionProvider.obj: oatpp/network/virtual_/server/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.obj

# target to build an object file
oatpp/network/virtual_/server/ConnectionProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.obj
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.cpp.obj

oatpp/network/virtual_/server/ConnectionProvider.i: oatpp/network/virtual_/server/ConnectionProvider.cpp.i
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.i

# target to preprocess a source file
oatpp/network/virtual_/server/ConnectionProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.i
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.cpp.i

oatpp/network/virtual_/server/ConnectionProvider.s: oatpp/network/virtual_/server/ConnectionProvider.cpp.s
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.s

# target to generate assembly for a file
oatpp/network/virtual_/server/ConnectionProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/network/virtual_/server/ConnectionProvider.cpp.s
.PHONY : oatpp/network/virtual_/server/ConnectionProvider.cpp.s

oatpp/orm/DbClient.obj: oatpp/orm/DbClient.cpp.obj
.PHONY : oatpp/orm/DbClient.obj

# target to build an object file
oatpp/orm/DbClient.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.obj
.PHONY : oatpp/orm/DbClient.cpp.obj

oatpp/orm/DbClient.i: oatpp/orm/DbClient.cpp.i
.PHONY : oatpp/orm/DbClient.i

# target to preprocess a source file
oatpp/orm/DbClient.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.i
.PHONY : oatpp/orm/DbClient.cpp.i

oatpp/orm/DbClient.s: oatpp/orm/DbClient.cpp.s
.PHONY : oatpp/orm/DbClient.s

# target to generate assembly for a file
oatpp/orm/DbClient.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/DbClient.cpp.s
.PHONY : oatpp/orm/DbClient.cpp.s

oatpp/orm/Executor.obj: oatpp/orm/Executor.cpp.obj
.PHONY : oatpp/orm/Executor.obj

# target to build an object file
oatpp/orm/Executor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.obj
.PHONY : oatpp/orm/Executor.cpp.obj

oatpp/orm/Executor.i: oatpp/orm/Executor.cpp.i
.PHONY : oatpp/orm/Executor.i

# target to preprocess a source file
oatpp/orm/Executor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.i
.PHONY : oatpp/orm/Executor.cpp.i

oatpp/orm/Executor.s: oatpp/orm/Executor.cpp.s
.PHONY : oatpp/orm/Executor.s

# target to generate assembly for a file
oatpp/orm/Executor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Executor.cpp.s
.PHONY : oatpp/orm/Executor.cpp.s

oatpp/orm/QueryResult.obj: oatpp/orm/QueryResult.cpp.obj
.PHONY : oatpp/orm/QueryResult.obj

# target to build an object file
oatpp/orm/QueryResult.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.obj
.PHONY : oatpp/orm/QueryResult.cpp.obj

oatpp/orm/QueryResult.i: oatpp/orm/QueryResult.cpp.i
.PHONY : oatpp/orm/QueryResult.i

# target to preprocess a source file
oatpp/orm/QueryResult.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.i
.PHONY : oatpp/orm/QueryResult.cpp.i

oatpp/orm/QueryResult.s: oatpp/orm/QueryResult.cpp.s
.PHONY : oatpp/orm/QueryResult.s

# target to generate assembly for a file
oatpp/orm/QueryResult.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/QueryResult.cpp.s
.PHONY : oatpp/orm/QueryResult.cpp.s

oatpp/orm/SchemaMigration.obj: oatpp/orm/SchemaMigration.cpp.obj
.PHONY : oatpp/orm/SchemaMigration.obj

# target to build an object file
oatpp/orm/SchemaMigration.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.obj
.PHONY : oatpp/orm/SchemaMigration.cpp.obj

oatpp/orm/SchemaMigration.i: oatpp/orm/SchemaMigration.cpp.i
.PHONY : oatpp/orm/SchemaMigration.i

# target to preprocess a source file
oatpp/orm/SchemaMigration.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.i
.PHONY : oatpp/orm/SchemaMigration.cpp.i

oatpp/orm/SchemaMigration.s: oatpp/orm/SchemaMigration.cpp.s
.PHONY : oatpp/orm/SchemaMigration.s

# target to generate assembly for a file
oatpp/orm/SchemaMigration.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/SchemaMigration.cpp.s
.PHONY : oatpp/orm/SchemaMigration.cpp.s

oatpp/orm/Transaction.obj: oatpp/orm/Transaction.cpp.obj
.PHONY : oatpp/orm/Transaction.obj

# target to build an object file
oatpp/orm/Transaction.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.obj
.PHONY : oatpp/orm/Transaction.cpp.obj

oatpp/orm/Transaction.i: oatpp/orm/Transaction.cpp.i
.PHONY : oatpp/orm/Transaction.i

# target to preprocess a source file
oatpp/orm/Transaction.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.i
.PHONY : oatpp/orm/Transaction.cpp.i

oatpp/orm/Transaction.s: oatpp/orm/Transaction.cpp.s
.PHONY : oatpp/orm/Transaction.s

# target to generate assembly for a file
oatpp/orm/Transaction.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/orm/Transaction.cpp.s
.PHONY : oatpp/orm/Transaction.cpp.s

oatpp/parser/json/Beautifier.obj: oatpp/parser/json/Beautifier.cpp.obj
.PHONY : oatpp/parser/json/Beautifier.obj

# target to build an object file
oatpp/parser/json/Beautifier.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.obj
.PHONY : oatpp/parser/json/Beautifier.cpp.obj

oatpp/parser/json/Beautifier.i: oatpp/parser/json/Beautifier.cpp.i
.PHONY : oatpp/parser/json/Beautifier.i

# target to preprocess a source file
oatpp/parser/json/Beautifier.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.i
.PHONY : oatpp/parser/json/Beautifier.cpp.i

oatpp/parser/json/Beautifier.s: oatpp/parser/json/Beautifier.cpp.s
.PHONY : oatpp/parser/json/Beautifier.s

# target to generate assembly for a file
oatpp/parser/json/Beautifier.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Beautifier.cpp.s
.PHONY : oatpp/parser/json/Beautifier.cpp.s

oatpp/parser/json/Utils.obj: oatpp/parser/json/Utils.cpp.obj
.PHONY : oatpp/parser/json/Utils.obj

# target to build an object file
oatpp/parser/json/Utils.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.obj
.PHONY : oatpp/parser/json/Utils.cpp.obj

oatpp/parser/json/Utils.i: oatpp/parser/json/Utils.cpp.i
.PHONY : oatpp/parser/json/Utils.i

# target to preprocess a source file
oatpp/parser/json/Utils.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.i
.PHONY : oatpp/parser/json/Utils.cpp.i

oatpp/parser/json/Utils.s: oatpp/parser/json/Utils.cpp.s
.PHONY : oatpp/parser/json/Utils.s

# target to generate assembly for a file
oatpp/parser/json/Utils.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/Utils.cpp.s
.PHONY : oatpp/parser/json/Utils.cpp.s

oatpp/parser/json/mapping/Deserializer.obj: oatpp/parser/json/mapping/Deserializer.cpp.obj
.PHONY : oatpp/parser/json/mapping/Deserializer.obj

# target to build an object file
oatpp/parser/json/mapping/Deserializer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.obj
.PHONY : oatpp/parser/json/mapping/Deserializer.cpp.obj

oatpp/parser/json/mapping/Deserializer.i: oatpp/parser/json/mapping/Deserializer.cpp.i
.PHONY : oatpp/parser/json/mapping/Deserializer.i

# target to preprocess a source file
oatpp/parser/json/mapping/Deserializer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.i
.PHONY : oatpp/parser/json/mapping/Deserializer.cpp.i

oatpp/parser/json/mapping/Deserializer.s: oatpp/parser/json/mapping/Deserializer.cpp.s
.PHONY : oatpp/parser/json/mapping/Deserializer.s

# target to generate assembly for a file
oatpp/parser/json/mapping/Deserializer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Deserializer.cpp.s
.PHONY : oatpp/parser/json/mapping/Deserializer.cpp.s

oatpp/parser/json/mapping/ObjectMapper.obj: oatpp/parser/json/mapping/ObjectMapper.cpp.obj
.PHONY : oatpp/parser/json/mapping/ObjectMapper.obj

# target to build an object file
oatpp/parser/json/mapping/ObjectMapper.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.obj
.PHONY : oatpp/parser/json/mapping/ObjectMapper.cpp.obj

oatpp/parser/json/mapping/ObjectMapper.i: oatpp/parser/json/mapping/ObjectMapper.cpp.i
.PHONY : oatpp/parser/json/mapping/ObjectMapper.i

# target to preprocess a source file
oatpp/parser/json/mapping/ObjectMapper.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.i
.PHONY : oatpp/parser/json/mapping/ObjectMapper.cpp.i

oatpp/parser/json/mapping/ObjectMapper.s: oatpp/parser/json/mapping/ObjectMapper.cpp.s
.PHONY : oatpp/parser/json/mapping/ObjectMapper.s

# target to generate assembly for a file
oatpp/parser/json/mapping/ObjectMapper.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/ObjectMapper.cpp.s
.PHONY : oatpp/parser/json/mapping/ObjectMapper.cpp.s

oatpp/parser/json/mapping/Serializer.obj: oatpp/parser/json/mapping/Serializer.cpp.obj
.PHONY : oatpp/parser/json/mapping/Serializer.obj

# target to build an object file
oatpp/parser/json/mapping/Serializer.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.obj
.PHONY : oatpp/parser/json/mapping/Serializer.cpp.obj

oatpp/parser/json/mapping/Serializer.i: oatpp/parser/json/mapping/Serializer.cpp.i
.PHONY : oatpp/parser/json/mapping/Serializer.i

# target to preprocess a source file
oatpp/parser/json/mapping/Serializer.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.i
.PHONY : oatpp/parser/json/mapping/Serializer.cpp.i

oatpp/parser/json/mapping/Serializer.s: oatpp/parser/json/mapping/Serializer.cpp.s
.PHONY : oatpp/parser/json/mapping/Serializer.s

# target to generate assembly for a file
oatpp/parser/json/mapping/Serializer.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/parser/json/mapping/Serializer.cpp.s
.PHONY : oatpp/parser/json/mapping/Serializer.cpp.s

oatpp/web/client/ApiClient.obj: oatpp/web/client/ApiClient.cpp.obj
.PHONY : oatpp/web/client/ApiClient.obj

# target to build an object file
oatpp/web/client/ApiClient.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.obj
.PHONY : oatpp/web/client/ApiClient.cpp.obj

oatpp/web/client/ApiClient.i: oatpp/web/client/ApiClient.cpp.i
.PHONY : oatpp/web/client/ApiClient.i

# target to preprocess a source file
oatpp/web/client/ApiClient.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.i
.PHONY : oatpp/web/client/ApiClient.cpp.i

oatpp/web/client/ApiClient.s: oatpp/web/client/ApiClient.cpp.s
.PHONY : oatpp/web/client/ApiClient.s

# target to generate assembly for a file
oatpp/web/client/ApiClient.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/ApiClient.cpp.s
.PHONY : oatpp/web/client/ApiClient.cpp.s

oatpp/web/client/HttpRequestExecutor.obj: oatpp/web/client/HttpRequestExecutor.cpp.obj
.PHONY : oatpp/web/client/HttpRequestExecutor.obj

# target to build an object file
oatpp/web/client/HttpRequestExecutor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.obj
.PHONY : oatpp/web/client/HttpRequestExecutor.cpp.obj

oatpp/web/client/HttpRequestExecutor.i: oatpp/web/client/HttpRequestExecutor.cpp.i
.PHONY : oatpp/web/client/HttpRequestExecutor.i

# target to preprocess a source file
oatpp/web/client/HttpRequestExecutor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.i
.PHONY : oatpp/web/client/HttpRequestExecutor.cpp.i

oatpp/web/client/HttpRequestExecutor.s: oatpp/web/client/HttpRequestExecutor.cpp.s
.PHONY : oatpp/web/client/HttpRequestExecutor.s

# target to generate assembly for a file
oatpp/web/client/HttpRequestExecutor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/HttpRequestExecutor.cpp.s
.PHONY : oatpp/web/client/HttpRequestExecutor.cpp.s

oatpp/web/client/RequestExecutor.obj: oatpp/web/client/RequestExecutor.cpp.obj
.PHONY : oatpp/web/client/RequestExecutor.obj

# target to build an object file
oatpp/web/client/RequestExecutor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.obj
.PHONY : oatpp/web/client/RequestExecutor.cpp.obj

oatpp/web/client/RequestExecutor.i: oatpp/web/client/RequestExecutor.cpp.i
.PHONY : oatpp/web/client/RequestExecutor.i

# target to preprocess a source file
oatpp/web/client/RequestExecutor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.i
.PHONY : oatpp/web/client/RequestExecutor.cpp.i

oatpp/web/client/RequestExecutor.s: oatpp/web/client/RequestExecutor.cpp.s
.PHONY : oatpp/web/client/RequestExecutor.s

# target to generate assembly for a file
oatpp/web/client/RequestExecutor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RequestExecutor.cpp.s
.PHONY : oatpp/web/client/RequestExecutor.cpp.s

oatpp/web/client/RetryPolicy.obj: oatpp/web/client/RetryPolicy.cpp.obj
.PHONY : oatpp/web/client/RetryPolicy.obj

# target to build an object file
oatpp/web/client/RetryPolicy.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.obj
.PHONY : oatpp/web/client/RetryPolicy.cpp.obj

oatpp/web/client/RetryPolicy.i: oatpp/web/client/RetryPolicy.cpp.i
.PHONY : oatpp/web/client/RetryPolicy.i

# target to preprocess a source file
oatpp/web/client/RetryPolicy.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.i
.PHONY : oatpp/web/client/RetryPolicy.cpp.i

oatpp/web/client/RetryPolicy.s: oatpp/web/client/RetryPolicy.cpp.s
.PHONY : oatpp/web/client/RetryPolicy.s

# target to generate assembly for a file
oatpp/web/client/RetryPolicy.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/client/RetryPolicy.cpp.s
.PHONY : oatpp/web/client/RetryPolicy.cpp.s

oatpp/web/mime/multipart/FileProvider.obj: oatpp/web/mime/multipart/FileProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/FileProvider.obj

# target to build an object file
oatpp/web/mime/multipart/FileProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/FileProvider.cpp.obj

oatpp/web/mime/multipart/FileProvider.i: oatpp/web/mime/multipart/FileProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/FileProvider.i

# target to preprocess a source file
oatpp/web/mime/multipart/FileProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/FileProvider.cpp.i

oatpp/web/mime/multipart/FileProvider.s: oatpp/web/mime/multipart/FileProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/FileProvider.s

# target to generate assembly for a file
oatpp/web/mime/multipart/FileProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/FileProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/FileProvider.cpp.s

oatpp/web/mime/multipart/InMemoryDataProvider.obj: oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.obj

# target to build an object file
oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.cpp.obj

oatpp/web/mime/multipart/InMemoryDataProvider.i: oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.i

# target to preprocess a source file
oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.cpp.i

oatpp/web/mime/multipart/InMemoryDataProvider.s: oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.s

# target to generate assembly for a file
oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/InMemoryDataProvider.cpp.s

oatpp/web/mime/multipart/Multipart.obj: oatpp/web/mime/multipart/Multipart.cpp.obj
.PHONY : oatpp/web/mime/multipart/Multipart.obj

# target to build an object file
oatpp/web/mime/multipart/Multipart.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.obj
.PHONY : oatpp/web/mime/multipart/Multipart.cpp.obj

oatpp/web/mime/multipart/Multipart.i: oatpp/web/mime/multipart/Multipart.cpp.i
.PHONY : oatpp/web/mime/multipart/Multipart.i

# target to preprocess a source file
oatpp/web/mime/multipart/Multipart.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.i
.PHONY : oatpp/web/mime/multipart/Multipart.cpp.i

oatpp/web/mime/multipart/Multipart.s: oatpp/web/mime/multipart/Multipart.cpp.s
.PHONY : oatpp/web/mime/multipart/Multipart.s

# target to generate assembly for a file
oatpp/web/mime/multipart/Multipart.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Multipart.cpp.s
.PHONY : oatpp/web/mime/multipart/Multipart.cpp.s

oatpp/web/mime/multipart/Part.obj: oatpp/web/mime/multipart/Part.cpp.obj
.PHONY : oatpp/web/mime/multipart/Part.obj

# target to build an object file
oatpp/web/mime/multipart/Part.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.obj
.PHONY : oatpp/web/mime/multipart/Part.cpp.obj

oatpp/web/mime/multipart/Part.i: oatpp/web/mime/multipart/Part.cpp.i
.PHONY : oatpp/web/mime/multipart/Part.i

# target to preprocess a source file
oatpp/web/mime/multipart/Part.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.i
.PHONY : oatpp/web/mime/multipart/Part.cpp.i

oatpp/web/mime/multipart/Part.s: oatpp/web/mime/multipart/Part.cpp.s
.PHONY : oatpp/web/mime/multipart/Part.s

# target to generate assembly for a file
oatpp/web/mime/multipart/Part.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Part.cpp.s
.PHONY : oatpp/web/mime/multipart/Part.cpp.s

oatpp/web/mime/multipart/PartList.obj: oatpp/web/mime/multipart/PartList.cpp.obj
.PHONY : oatpp/web/mime/multipart/PartList.obj

# target to build an object file
oatpp/web/mime/multipart/PartList.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.obj
.PHONY : oatpp/web/mime/multipart/PartList.cpp.obj

oatpp/web/mime/multipart/PartList.i: oatpp/web/mime/multipart/PartList.cpp.i
.PHONY : oatpp/web/mime/multipart/PartList.i

# target to preprocess a source file
oatpp/web/mime/multipart/PartList.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.i
.PHONY : oatpp/web/mime/multipart/PartList.cpp.i

oatpp/web/mime/multipart/PartList.s: oatpp/web/mime/multipart/PartList.cpp.s
.PHONY : oatpp/web/mime/multipart/PartList.s

# target to generate assembly for a file
oatpp/web/mime/multipart/PartList.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartList.cpp.s
.PHONY : oatpp/web/mime/multipart/PartList.cpp.s

oatpp/web/mime/multipart/PartReader.obj: oatpp/web/mime/multipart/PartReader.cpp.obj
.PHONY : oatpp/web/mime/multipart/PartReader.obj

# target to build an object file
oatpp/web/mime/multipart/PartReader.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.obj
.PHONY : oatpp/web/mime/multipart/PartReader.cpp.obj

oatpp/web/mime/multipart/PartReader.i: oatpp/web/mime/multipart/PartReader.cpp.i
.PHONY : oatpp/web/mime/multipart/PartReader.i

# target to preprocess a source file
oatpp/web/mime/multipart/PartReader.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.i
.PHONY : oatpp/web/mime/multipart/PartReader.cpp.i

oatpp/web/mime/multipart/PartReader.s: oatpp/web/mime/multipart/PartReader.cpp.s
.PHONY : oatpp/web/mime/multipart/PartReader.s

# target to generate assembly for a file
oatpp/web/mime/multipart/PartReader.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/PartReader.cpp.s
.PHONY : oatpp/web/mime/multipart/PartReader.cpp.s

oatpp/web/mime/multipart/Reader.obj: oatpp/web/mime/multipart/Reader.cpp.obj
.PHONY : oatpp/web/mime/multipart/Reader.obj

# target to build an object file
oatpp/web/mime/multipart/Reader.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.obj
.PHONY : oatpp/web/mime/multipart/Reader.cpp.obj

oatpp/web/mime/multipart/Reader.i: oatpp/web/mime/multipart/Reader.cpp.i
.PHONY : oatpp/web/mime/multipart/Reader.i

# target to preprocess a source file
oatpp/web/mime/multipart/Reader.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.i
.PHONY : oatpp/web/mime/multipart/Reader.cpp.i

oatpp/web/mime/multipart/Reader.s: oatpp/web/mime/multipart/Reader.cpp.s
.PHONY : oatpp/web/mime/multipart/Reader.s

# target to generate assembly for a file
oatpp/web/mime/multipart/Reader.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/Reader.cpp.s
.PHONY : oatpp/web/mime/multipart/Reader.cpp.s

oatpp/web/mime/multipart/StatefulParser.obj: oatpp/web/mime/multipart/StatefulParser.cpp.obj
.PHONY : oatpp/web/mime/multipart/StatefulParser.obj

# target to build an object file
oatpp/web/mime/multipart/StatefulParser.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.obj
.PHONY : oatpp/web/mime/multipart/StatefulParser.cpp.obj

oatpp/web/mime/multipart/StatefulParser.i: oatpp/web/mime/multipart/StatefulParser.cpp.i
.PHONY : oatpp/web/mime/multipart/StatefulParser.i

# target to preprocess a source file
oatpp/web/mime/multipart/StatefulParser.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.i
.PHONY : oatpp/web/mime/multipart/StatefulParser.cpp.i

oatpp/web/mime/multipart/StatefulParser.s: oatpp/web/mime/multipart/StatefulParser.cpp.s
.PHONY : oatpp/web/mime/multipart/StatefulParser.s

# target to generate assembly for a file
oatpp/web/mime/multipart/StatefulParser.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/StatefulParser.cpp.s
.PHONY : oatpp/web/mime/multipart/StatefulParser.cpp.s

oatpp/web/mime/multipart/TemporaryFileProvider.obj: oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.obj

# target to build an object file
oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.cpp.obj

oatpp/web/mime/multipart/TemporaryFileProvider.i: oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.i

# target to preprocess a source file
oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.cpp.i

oatpp/web/mime/multipart/TemporaryFileProvider.s: oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.s

# target to generate assembly for a file
oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s
.PHONY : oatpp/web/mime/multipart/TemporaryFileProvider.cpp.s

oatpp/web/protocol/CommunicationError.obj: oatpp/web/protocol/CommunicationError.cpp.obj
.PHONY : oatpp/web/protocol/CommunicationError.obj

# target to build an object file
oatpp/web/protocol/CommunicationError.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.obj
.PHONY : oatpp/web/protocol/CommunicationError.cpp.obj

oatpp/web/protocol/CommunicationError.i: oatpp/web/protocol/CommunicationError.cpp.i
.PHONY : oatpp/web/protocol/CommunicationError.i

# target to preprocess a source file
oatpp/web/protocol/CommunicationError.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.i
.PHONY : oatpp/web/protocol/CommunicationError.cpp.i

oatpp/web/protocol/CommunicationError.s: oatpp/web/protocol/CommunicationError.cpp.s
.PHONY : oatpp/web/protocol/CommunicationError.s

# target to generate assembly for a file
oatpp/web/protocol/CommunicationError.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/CommunicationError.cpp.s
.PHONY : oatpp/web/protocol/CommunicationError.cpp.s

oatpp/web/protocol/http/Http.obj: oatpp/web/protocol/http/Http.cpp.obj
.PHONY : oatpp/web/protocol/http/Http.obj

# target to build an object file
oatpp/web/protocol/http/Http.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.obj
.PHONY : oatpp/web/protocol/http/Http.cpp.obj

oatpp/web/protocol/http/Http.i: oatpp/web/protocol/http/Http.cpp.i
.PHONY : oatpp/web/protocol/http/Http.i

# target to preprocess a source file
oatpp/web/protocol/http/Http.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.i
.PHONY : oatpp/web/protocol/http/Http.cpp.i

oatpp/web/protocol/http/Http.s: oatpp/web/protocol/http/Http.cpp.s
.PHONY : oatpp/web/protocol/http/Http.s

# target to generate assembly for a file
oatpp/web/protocol/http/Http.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/Http.cpp.s
.PHONY : oatpp/web/protocol/http/Http.cpp.s

oatpp/web/protocol/http/encoding/Chunked.obj: oatpp/web/protocol/http/encoding/Chunked.cpp.obj
.PHONY : oatpp/web/protocol/http/encoding/Chunked.obj

# target to build an object file
oatpp/web/protocol/http/encoding/Chunked.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.obj
.PHONY : oatpp/web/protocol/http/encoding/Chunked.cpp.obj

oatpp/web/protocol/http/encoding/Chunked.i: oatpp/web/protocol/http/encoding/Chunked.cpp.i
.PHONY : oatpp/web/protocol/http/encoding/Chunked.i

# target to preprocess a source file
oatpp/web/protocol/http/encoding/Chunked.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.i
.PHONY : oatpp/web/protocol/http/encoding/Chunked.cpp.i

oatpp/web/protocol/http/encoding/Chunked.s: oatpp/web/protocol/http/encoding/Chunked.cpp.s
.PHONY : oatpp/web/protocol/http/encoding/Chunked.s

# target to generate assembly for a file
oatpp/web/protocol/http/encoding/Chunked.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/Chunked.cpp.s
.PHONY : oatpp/web/protocol/http/encoding/Chunked.cpp.s

oatpp/web/protocol/http/encoding/ProviderCollection.obj: oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.obj

# target to build an object file
oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.cpp.obj

oatpp/web/protocol/http/encoding/ProviderCollection.i: oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.i

# target to preprocess a source file
oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.cpp.i

oatpp/web/protocol/http/encoding/ProviderCollection.s: oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.s

# target to generate assembly for a file
oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s
.PHONY : oatpp/web/protocol/http/encoding/ProviderCollection.cpp.s

oatpp/web/protocol/http/incoming/BodyDecoder.obj: oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.obj

# target to build an object file
oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.cpp.obj

oatpp/web/protocol/http/incoming/BodyDecoder.i: oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.cpp.i

oatpp/web/protocol/http/incoming/BodyDecoder.s: oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/BodyDecoder.cpp.s

oatpp/web/protocol/http/incoming/Request.obj: oatpp/web/protocol/http/incoming/Request.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/Request.obj

# target to build an object file
oatpp/web/protocol/http/incoming/Request.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/Request.cpp.obj

oatpp/web/protocol/http/incoming/Request.i: oatpp/web/protocol/http/incoming/Request.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/Request.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/Request.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/Request.cpp.i

oatpp/web/protocol/http/incoming/Request.s: oatpp/web/protocol/http/incoming/Request.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/Request.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/Request.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Request.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/Request.cpp.s

oatpp/web/protocol/http/incoming/RequestHeadersReader.obj: oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.obj

# target to build an object file
oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.obj

oatpp/web/protocol/http/incoming/RequestHeadersReader.i: oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.i

oatpp/web/protocol/http/incoming/RequestHeadersReader.s: oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp.s

oatpp/web/protocol/http/incoming/Response.obj: oatpp/web/protocol/http/incoming/Response.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/Response.obj

# target to build an object file
oatpp/web/protocol/http/incoming/Response.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/Response.cpp.obj

oatpp/web/protocol/http/incoming/Response.i: oatpp/web/protocol/http/incoming/Response.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/Response.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/Response.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/Response.cpp.i

oatpp/web/protocol/http/incoming/Response.s: oatpp/web/protocol/http/incoming/Response.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/Response.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/Response.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/Response.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/Response.cpp.s

oatpp/web/protocol/http/incoming/ResponseHeadersReader.obj: oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.obj

# target to build an object file
oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.obj

oatpp/web/protocol/http/incoming/ResponseHeadersReader.i: oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.i

oatpp/web/protocol/http/incoming/ResponseHeadersReader.s: oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp.s

oatpp/web/protocol/http/incoming/SimpleBodyDecoder.obj: oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.obj

# target to build an object file
oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.obj

oatpp/web/protocol/http/incoming/SimpleBodyDecoder.i: oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.i

# target to preprocess a source file
oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.i

oatpp/web/protocol/http/incoming/SimpleBodyDecoder.s: oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.s

# target to generate assembly for a file
oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s
.PHONY : oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp.s

oatpp/web/protocol/http/outgoing/Body.obj: oatpp/web/protocol/http/outgoing/Body.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Body.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/Body.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Body.cpp.obj

oatpp/web/protocol/http/outgoing/Body.i: oatpp/web/protocol/http/outgoing/Body.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Body.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/Body.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Body.cpp.i

oatpp/web/protocol/http/outgoing/Body.s: oatpp/web/protocol/http/outgoing/Body.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Body.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/Body.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Body.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Body.cpp.s

oatpp/web/protocol/http/outgoing/BufferBody.obj: oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.cpp.obj

oatpp/web/protocol/http/outgoing/BufferBody.i: oatpp/web/protocol/http/outgoing/BufferBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/BufferBody.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.cpp.i

oatpp/web/protocol/http/outgoing/BufferBody.s: oatpp/web/protocol/http/outgoing/BufferBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/BufferBody.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/BufferBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/BufferBody.cpp.s

oatpp/web/protocol/http/outgoing/MultipartBody.obj: oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.cpp.obj

oatpp/web/protocol/http/outgoing/MultipartBody.i: oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.cpp.i

oatpp/web/protocol/http/outgoing/MultipartBody.s: oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/MultipartBody.cpp.s

oatpp/web/protocol/http/outgoing/Request.obj: oatpp/web/protocol/http/outgoing/Request.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Request.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/Request.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Request.cpp.obj

oatpp/web/protocol/http/outgoing/Request.i: oatpp/web/protocol/http/outgoing/Request.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Request.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/Request.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Request.cpp.i

oatpp/web/protocol/http/outgoing/Request.s: oatpp/web/protocol/http/outgoing/Request.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Request.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/Request.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Request.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Request.cpp.s

oatpp/web/protocol/http/outgoing/Response.obj: oatpp/web/protocol/http/outgoing/Response.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Response.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/Response.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/Response.cpp.obj

oatpp/web/protocol/http/outgoing/Response.i: oatpp/web/protocol/http/outgoing/Response.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Response.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/Response.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/Response.cpp.i

oatpp/web/protocol/http/outgoing/Response.s: oatpp/web/protocol/http/outgoing/Response.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Response.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/Response.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/Response.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/Response.cpp.s

oatpp/web/protocol/http/outgoing/ResponseFactory.obj: oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.obj

oatpp/web/protocol/http/outgoing/ResponseFactory.i: oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.i

oatpp/web/protocol/http/outgoing/ResponseFactory.s: oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/ResponseFactory.cpp.s

oatpp/web/protocol/http/outgoing/StreamingBody.obj: oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.obj

# target to build an object file
oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.cpp.obj

oatpp/web/protocol/http/outgoing/StreamingBody.i: oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.i

# target to preprocess a source file
oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.cpp.i

oatpp/web/protocol/http/outgoing/StreamingBody.s: oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.s

# target to generate assembly for a file
oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s
.PHONY : oatpp/web/protocol/http/outgoing/StreamingBody.cpp.s

oatpp/web/protocol/http/utils/CommunicationUtils.obj: oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.obj

# target to build an object file
oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.cpp.obj

oatpp/web/protocol/http/utils/CommunicationUtils.i: oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.i

# target to preprocess a source file
oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.cpp.i

oatpp/web/protocol/http/utils/CommunicationUtils.s: oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.s

# target to generate assembly for a file
oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s
.PHONY : oatpp/web/protocol/http/utils/CommunicationUtils.cpp.s

oatpp/web/server/AsyncHttpConnectionHandler.obj: oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.obj

# target to build an object file
oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.cpp.obj

oatpp/web/server/AsyncHttpConnectionHandler.i: oatpp/web/server/AsyncHttpConnectionHandler.cpp.i
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.i

# target to preprocess a source file
oatpp/web/server/AsyncHttpConnectionHandler.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.i
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.cpp.i

oatpp/web/server/AsyncHttpConnectionHandler.s: oatpp/web/server/AsyncHttpConnectionHandler.cpp.s
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.s

# target to generate assembly for a file
oatpp/web/server/AsyncHttpConnectionHandler.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/AsyncHttpConnectionHandler.cpp.s
.PHONY : oatpp/web/server/AsyncHttpConnectionHandler.cpp.s

oatpp/web/server/HttpConnectionHandler.obj: oatpp/web/server/HttpConnectionHandler.cpp.obj
.PHONY : oatpp/web/server/HttpConnectionHandler.obj

# target to build an object file
oatpp/web/server/HttpConnectionHandler.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.obj
.PHONY : oatpp/web/server/HttpConnectionHandler.cpp.obj

oatpp/web/server/HttpConnectionHandler.i: oatpp/web/server/HttpConnectionHandler.cpp.i
.PHONY : oatpp/web/server/HttpConnectionHandler.i

# target to preprocess a source file
oatpp/web/server/HttpConnectionHandler.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.i
.PHONY : oatpp/web/server/HttpConnectionHandler.cpp.i

oatpp/web/server/HttpConnectionHandler.s: oatpp/web/server/HttpConnectionHandler.cpp.s
.PHONY : oatpp/web/server/HttpConnectionHandler.s

# target to generate assembly for a file
oatpp/web/server/HttpConnectionHandler.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpConnectionHandler.cpp.s
.PHONY : oatpp/web/server/HttpConnectionHandler.cpp.s

oatpp/web/server/HttpProcessor.obj: oatpp/web/server/HttpProcessor.cpp.obj
.PHONY : oatpp/web/server/HttpProcessor.obj

# target to build an object file
oatpp/web/server/HttpProcessor.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.obj
.PHONY : oatpp/web/server/HttpProcessor.cpp.obj

oatpp/web/server/HttpProcessor.i: oatpp/web/server/HttpProcessor.cpp.i
.PHONY : oatpp/web/server/HttpProcessor.i

# target to preprocess a source file
oatpp/web/server/HttpProcessor.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.i
.PHONY : oatpp/web/server/HttpProcessor.cpp.i

oatpp/web/server/HttpProcessor.s: oatpp/web/server/HttpProcessor.cpp.s
.PHONY : oatpp/web/server/HttpProcessor.s

# target to generate assembly for a file
oatpp/web/server/HttpProcessor.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpProcessor.cpp.s
.PHONY : oatpp/web/server/HttpProcessor.cpp.s

oatpp/web/server/HttpRouter.obj: oatpp/web/server/HttpRouter.cpp.obj
.PHONY : oatpp/web/server/HttpRouter.obj

# target to build an object file
oatpp/web/server/HttpRouter.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.obj
.PHONY : oatpp/web/server/HttpRouter.cpp.obj

oatpp/web/server/HttpRouter.i: oatpp/web/server/HttpRouter.cpp.i
.PHONY : oatpp/web/server/HttpRouter.i

# target to preprocess a source file
oatpp/web/server/HttpRouter.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.i
.PHONY : oatpp/web/server/HttpRouter.cpp.i

oatpp/web/server/HttpRouter.s: oatpp/web/server/HttpRouter.cpp.s
.PHONY : oatpp/web/server/HttpRouter.s

# target to generate assembly for a file
oatpp/web/server/HttpRouter.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/HttpRouter.cpp.s
.PHONY : oatpp/web/server/HttpRouter.cpp.s

oatpp/web/server/api/ApiController.obj: oatpp/web/server/api/ApiController.cpp.obj
.PHONY : oatpp/web/server/api/ApiController.obj

# target to build an object file
oatpp/web/server/api/ApiController.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.obj
.PHONY : oatpp/web/server/api/ApiController.cpp.obj

oatpp/web/server/api/ApiController.i: oatpp/web/server/api/ApiController.cpp.i
.PHONY : oatpp/web/server/api/ApiController.i

# target to preprocess a source file
oatpp/web/server/api/ApiController.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.i
.PHONY : oatpp/web/server/api/ApiController.cpp.i

oatpp/web/server/api/ApiController.s: oatpp/web/server/api/ApiController.cpp.s
.PHONY : oatpp/web/server/api/ApiController.s

# target to generate assembly for a file
oatpp/web/server/api/ApiController.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/ApiController.cpp.s
.PHONY : oatpp/web/server/api/ApiController.cpp.s

oatpp/web/server/api/Endpoint.obj: oatpp/web/server/api/Endpoint.cpp.obj
.PHONY : oatpp/web/server/api/Endpoint.obj

# target to build an object file
oatpp/web/server/api/Endpoint.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.obj
.PHONY : oatpp/web/server/api/Endpoint.cpp.obj

oatpp/web/server/api/Endpoint.i: oatpp/web/server/api/Endpoint.cpp.i
.PHONY : oatpp/web/server/api/Endpoint.i

# target to preprocess a source file
oatpp/web/server/api/Endpoint.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.i
.PHONY : oatpp/web/server/api/Endpoint.cpp.i

oatpp/web/server/api/Endpoint.s: oatpp/web/server/api/Endpoint.cpp.s
.PHONY : oatpp/web/server/api/Endpoint.s

# target to generate assembly for a file
oatpp/web/server/api/Endpoint.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/api/Endpoint.cpp.s
.PHONY : oatpp/web/server/api/Endpoint.cpp.s

oatpp/web/server/handler/AuthorizationHandler.obj: oatpp/web/server/handler/AuthorizationHandler.cpp.obj
.PHONY : oatpp/web/server/handler/AuthorizationHandler.obj

# target to build an object file
oatpp/web/server/handler/AuthorizationHandler.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.obj
.PHONY : oatpp/web/server/handler/AuthorizationHandler.cpp.obj

oatpp/web/server/handler/AuthorizationHandler.i: oatpp/web/server/handler/AuthorizationHandler.cpp.i
.PHONY : oatpp/web/server/handler/AuthorizationHandler.i

# target to preprocess a source file
oatpp/web/server/handler/AuthorizationHandler.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.i
.PHONY : oatpp/web/server/handler/AuthorizationHandler.cpp.i

oatpp/web/server/handler/AuthorizationHandler.s: oatpp/web/server/handler/AuthorizationHandler.cpp.s
.PHONY : oatpp/web/server/handler/AuthorizationHandler.s

# target to generate assembly for a file
oatpp/web/server/handler/AuthorizationHandler.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/AuthorizationHandler.cpp.s
.PHONY : oatpp/web/server/handler/AuthorizationHandler.cpp.s

oatpp/web/server/handler/ErrorHandler.obj: oatpp/web/server/handler/ErrorHandler.cpp.obj
.PHONY : oatpp/web/server/handler/ErrorHandler.obj

# target to build an object file
oatpp/web/server/handler/ErrorHandler.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.obj
.PHONY : oatpp/web/server/handler/ErrorHandler.cpp.obj

oatpp/web/server/handler/ErrorHandler.i: oatpp/web/server/handler/ErrorHandler.cpp.i
.PHONY : oatpp/web/server/handler/ErrorHandler.i

# target to preprocess a source file
oatpp/web/server/handler/ErrorHandler.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.i
.PHONY : oatpp/web/server/handler/ErrorHandler.cpp.i

oatpp/web/server/handler/ErrorHandler.s: oatpp/web/server/handler/ErrorHandler.cpp.s
.PHONY : oatpp/web/server/handler/ErrorHandler.s

# target to generate assembly for a file
oatpp/web/server/handler/ErrorHandler.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/handler/ErrorHandler.cpp.s
.PHONY : oatpp/web/server/handler/ErrorHandler.cpp.s

oatpp/web/server/interceptor/AllowCorsGlobal.obj: oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.obj

# target to build an object file
oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.cpp.obj

oatpp/web/server/interceptor/AllowCorsGlobal.i: oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.i

# target to preprocess a source file
oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.cpp.i

oatpp/web/server/interceptor/AllowCorsGlobal.s: oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.s

# target to generate assembly for a file
oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s
.PHONY : oatpp/web/server/interceptor/AllowCorsGlobal.cpp.s

oatpp/web/url/mapping/Pattern.obj: oatpp/web/url/mapping/Pattern.cpp.obj
.PHONY : oatpp/web/url/mapping/Pattern.obj

# target to build an object file
oatpp/web/url/mapping/Pattern.cpp.obj:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.obj
.PHONY : oatpp/web/url/mapping/Pattern.cpp.obj

oatpp/web/url/mapping/Pattern.i: oatpp/web/url/mapping/Pattern.cpp.i
.PHONY : oatpp/web/url/mapping/Pattern.i

# target to preprocess a source file
oatpp/web/url/mapping/Pattern.cpp.i:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.i
.PHONY : oatpp/web/url/mapping/Pattern.cpp.i

oatpp/web/url/mapping/Pattern.s: oatpp/web/url/mapping/Pattern.cpp.s
.PHONY : oatpp/web/url/mapping/Pattern.s

# target to generate assembly for a file
oatpp/web/url/mapping/Pattern.cpp.s:
	cd /d D:\MiWebApp\WebServerApp\build && $(MAKE) $(MAKESILENT) -f Oat++\CMakeFiles\oatpp.dir\build.make Oat++/CMakeFiles/oatpp.dir/oatpp/web/url/mapping/Pattern.cpp.s
.PHONY : oatpp/web/url/mapping/Pattern.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... codegen
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... oatpp
	@echo ... oatpp/algorithm/CRC.obj
	@echo ... oatpp/algorithm/CRC.i
	@echo ... oatpp/algorithm/CRC.s
	@echo ... oatpp/core/IODefinitions.obj
	@echo ... oatpp/core/IODefinitions.i
	@echo ... oatpp/core/IODefinitions.s
	@echo ... oatpp/core/async/ConditionVariable.obj
	@echo ... oatpp/core/async/ConditionVariable.i
	@echo ... oatpp/core/async/ConditionVariable.s
	@echo ... oatpp/core/async/Coroutine.obj
	@echo ... oatpp/core/async/Coroutine.i
	@echo ... oatpp/core/async/Coroutine.s
	@echo ... oatpp/core/async/CoroutineWaitList.obj
	@echo ... oatpp/core/async/CoroutineWaitList.i
	@echo ... oatpp/core/async/CoroutineWaitList.s
	@echo ... oatpp/core/async/Error.obj
	@echo ... oatpp/core/async/Error.i
	@echo ... oatpp/core/async/Error.s
	@echo ... oatpp/core/async/Executor.obj
	@echo ... oatpp/core/async/Executor.i
	@echo ... oatpp/core/async/Executor.s
	@echo ... oatpp/core/async/Lock.obj
	@echo ... oatpp/core/async/Lock.i
	@echo ... oatpp/core/async/Lock.s
	@echo ... oatpp/core/async/Processor.obj
	@echo ... oatpp/core/async/Processor.i
	@echo ... oatpp/core/async/Processor.s
	@echo ... oatpp/core/async/worker/IOEventWorker_common.obj
	@echo ... oatpp/core/async/worker/IOEventWorker_common.i
	@echo ... oatpp/core/async/worker/IOEventWorker_common.s
	@echo ... oatpp/core/async/worker/IOEventWorker_epoll.obj
	@echo ... oatpp/core/async/worker/IOEventWorker_epoll.i
	@echo ... oatpp/core/async/worker/IOEventWorker_epoll.s
	@echo ... oatpp/core/async/worker/IOEventWorker_kqueue.obj
	@echo ... oatpp/core/async/worker/IOEventWorker_kqueue.i
	@echo ... oatpp/core/async/worker/IOEventWorker_kqueue.s
	@echo ... oatpp/core/async/worker/IOEventWorker_stub.obj
	@echo ... oatpp/core/async/worker/IOEventWorker_stub.i
	@echo ... oatpp/core/async/worker/IOEventWorker_stub.s
	@echo ... oatpp/core/async/worker/IOWorker.obj
	@echo ... oatpp/core/async/worker/IOWorker.i
	@echo ... oatpp/core/async/worker/IOWorker.s
	@echo ... oatpp/core/async/worker/TimerWorker.obj
	@echo ... oatpp/core/async/worker/TimerWorker.i
	@echo ... oatpp/core/async/worker/TimerWorker.s
	@echo ... oatpp/core/async/worker/Worker.obj
	@echo ... oatpp/core/async/worker/Worker.i
	@echo ... oatpp/core/async/worker/Worker.s
	@echo ... oatpp/core/base/CommandLineArguments.obj
	@echo ... oatpp/core/base/CommandLineArguments.i
	@echo ... oatpp/core/base/CommandLineArguments.s
	@echo ... oatpp/core/base/Countable.obj
	@echo ... oatpp/core/base/Countable.i
	@echo ... oatpp/core/base/Countable.s
	@echo ... oatpp/core/base/Environment.obj
	@echo ... oatpp/core/base/Environment.i
	@echo ... oatpp/core/base/Environment.s
	@echo ... oatpp/core/concurrency/SpinLock.obj
	@echo ... oatpp/core/concurrency/SpinLock.i
	@echo ... oatpp/core/concurrency/SpinLock.s
	@echo ... oatpp/core/concurrency/Thread.obj
	@echo ... oatpp/core/concurrency/Thread.i
	@echo ... oatpp/core/concurrency/Thread.s
	@echo ... oatpp/core/data/Bundle.obj
	@echo ... oatpp/core/data/Bundle.i
	@echo ... oatpp/core/data/Bundle.s
	@echo ... oatpp/core/data/buffer/FIFOBuffer.obj
	@echo ... oatpp/core/data/buffer/FIFOBuffer.i
	@echo ... oatpp/core/data/buffer/FIFOBuffer.s
	@echo ... oatpp/core/data/buffer/IOBuffer.obj
	@echo ... oatpp/core/data/buffer/IOBuffer.i
	@echo ... oatpp/core/data/buffer/IOBuffer.s
	@echo ... oatpp/core/data/buffer/Processor.obj
	@echo ... oatpp/core/data/buffer/Processor.i
	@echo ... oatpp/core/data/buffer/Processor.s
	@echo ... oatpp/core/data/mapping/ObjectMapper.obj
	@echo ... oatpp/core/data/mapping/ObjectMapper.i
	@echo ... oatpp/core/data/mapping/ObjectMapper.s
	@echo ... oatpp/core/data/mapping/TypeResolver.obj
	@echo ... oatpp/core/data/mapping/TypeResolver.i
	@echo ... oatpp/core/data/mapping/TypeResolver.s
	@echo ... oatpp/core/data/mapping/type/Any.obj
	@echo ... oatpp/core/data/mapping/type/Any.i
	@echo ... oatpp/core/data/mapping/type/Any.s
	@echo ... oatpp/core/data/mapping/type/Enum.obj
	@echo ... oatpp/core/data/mapping/type/Enum.i
	@echo ... oatpp/core/data/mapping/type/Enum.s
	@echo ... oatpp/core/data/mapping/type/List.obj
	@echo ... oatpp/core/data/mapping/type/List.i
	@echo ... oatpp/core/data/mapping/type/List.s
	@echo ... oatpp/core/data/mapping/type/Object.obj
	@echo ... oatpp/core/data/mapping/type/Object.i
	@echo ... oatpp/core/data/mapping/type/Object.s
	@echo ... oatpp/core/data/mapping/type/PairList.obj
	@echo ... oatpp/core/data/mapping/type/PairList.i
	@echo ... oatpp/core/data/mapping/type/PairList.s
	@echo ... oatpp/core/data/mapping/type/Primitive.obj
	@echo ... oatpp/core/data/mapping/type/Primitive.i
	@echo ... oatpp/core/data/mapping/type/Primitive.s
	@echo ... oatpp/core/data/mapping/type/Type.obj
	@echo ... oatpp/core/data/mapping/type/Type.i
	@echo ... oatpp/core/data/mapping/type/Type.s
	@echo ... oatpp/core/data/mapping/type/UnorderedMap.obj
	@echo ... oatpp/core/data/mapping/type/UnorderedMap.i
	@echo ... oatpp/core/data/mapping/type/UnorderedMap.s
	@echo ... oatpp/core/data/mapping/type/UnorderedSet.obj
	@echo ... oatpp/core/data/mapping/type/UnorderedSet.i
	@echo ... oatpp/core/data/mapping/type/UnorderedSet.s
	@echo ... oatpp/core/data/mapping/type/Vector.obj
	@echo ... oatpp/core/data/mapping/type/Vector.i
	@echo ... oatpp/core/data/mapping/type/Vector.s
	@echo ... oatpp/core/data/resource/File.obj
	@echo ... oatpp/core/data/resource/File.i
	@echo ... oatpp/core/data/resource/File.s
	@echo ... oatpp/core/data/resource/InMemoryData.obj
	@echo ... oatpp/core/data/resource/InMemoryData.i
	@echo ... oatpp/core/data/resource/InMemoryData.s
	@echo ... oatpp/core/data/resource/TemporaryFile.obj
	@echo ... oatpp/core/data/resource/TemporaryFile.i
	@echo ... oatpp/core/data/resource/TemporaryFile.s
	@echo ... oatpp/core/data/share/MemoryLabel.obj
	@echo ... oatpp/core/data/share/MemoryLabel.i
	@echo ... oatpp/core/data/share/MemoryLabel.s
	@echo ... oatpp/core/data/share/StringTemplate.obj
	@echo ... oatpp/core/data/share/StringTemplate.i
	@echo ... oatpp/core/data/share/StringTemplate.s
	@echo ... oatpp/core/data/stream/BufferStream.obj
	@echo ... oatpp/core/data/stream/BufferStream.i
	@echo ... oatpp/core/data/stream/BufferStream.s
	@echo ... oatpp/core/data/stream/FIFOStream.obj
	@echo ... oatpp/core/data/stream/FIFOStream.i
	@echo ... oatpp/core/data/stream/FIFOStream.s
	@echo ... oatpp/core/data/stream/FileStream.obj
	@echo ... oatpp/core/data/stream/FileStream.i
	@echo ... oatpp/core/data/stream/FileStream.s
	@echo ... oatpp/core/data/stream/Stream.obj
	@echo ... oatpp/core/data/stream/Stream.i
	@echo ... oatpp/core/data/stream/Stream.s
	@echo ... oatpp/core/data/stream/StreamBufferedProxy.obj
	@echo ... oatpp/core/data/stream/StreamBufferedProxy.i
	@echo ... oatpp/core/data/stream/StreamBufferedProxy.s
	@echo ... oatpp/core/parser/Caret.obj
	@echo ... oatpp/core/parser/Caret.i
	@echo ... oatpp/core/parser/Caret.s
	@echo ... oatpp/core/parser/ParsingError.obj
	@echo ... oatpp/core/parser/ParsingError.i
	@echo ... oatpp/core/parser/ParsingError.s
	@echo ... oatpp/core/utils/Binary.obj
	@echo ... oatpp/core/utils/Binary.i
	@echo ... oatpp/core/utils/Binary.s
	@echo ... oatpp/core/utils/ConversionUtils.obj
	@echo ... oatpp/core/utils/ConversionUtils.i
	@echo ... oatpp/core/utils/ConversionUtils.s
	@echo ... oatpp/core/utils/Random.obj
	@echo ... oatpp/core/utils/Random.i
	@echo ... oatpp/core/utils/Random.s
	@echo ... oatpp/core/utils/String.obj
	@echo ... oatpp/core/utils/String.i
	@echo ... oatpp/core/utils/String.s
	@echo ... oatpp/encoding/Base64.obj
	@echo ... oatpp/encoding/Base64.i
	@echo ... oatpp/encoding/Base64.s
	@echo ... oatpp/encoding/Hex.obj
	@echo ... oatpp/encoding/Hex.i
	@echo ... oatpp/encoding/Hex.s
	@echo ... oatpp/encoding/Unicode.obj
	@echo ... oatpp/encoding/Unicode.i
	@echo ... oatpp/encoding/Unicode.s
	@echo ... oatpp/encoding/Url.obj
	@echo ... oatpp/encoding/Url.i
	@echo ... oatpp/encoding/Url.s
	@echo ... oatpp/network/Address.obj
	@echo ... oatpp/network/Address.i
	@echo ... oatpp/network/Address.s
	@echo ... oatpp/network/ConnectionPool.obj
	@echo ... oatpp/network/ConnectionPool.i
	@echo ... oatpp/network/ConnectionPool.s
	@echo ... oatpp/network/ConnectionProvider.obj
	@echo ... oatpp/network/ConnectionProvider.i
	@echo ... oatpp/network/ConnectionProvider.s
	@echo ... oatpp/network/ConnectionProviderSwitch.obj
	@echo ... oatpp/network/ConnectionProviderSwitch.i
	@echo ... oatpp/network/ConnectionProviderSwitch.s
	@echo ... oatpp/network/Server.obj
	@echo ... oatpp/network/Server.i
	@echo ... oatpp/network/Server.s
	@echo ... oatpp/network/Url.obj
	@echo ... oatpp/network/Url.i
	@echo ... oatpp/network/Url.s
	@echo ... oatpp/network/monitor/ConnectionInactivityChecker.obj
	@echo ... oatpp/network/monitor/ConnectionInactivityChecker.i
	@echo ... oatpp/network/monitor/ConnectionInactivityChecker.s
	@echo ... oatpp/network/monitor/ConnectionMaxAgeChecker.obj
	@echo ... oatpp/network/monitor/ConnectionMaxAgeChecker.i
	@echo ... oatpp/network/monitor/ConnectionMaxAgeChecker.s
	@echo ... oatpp/network/monitor/ConnectionMonitor.obj
	@echo ... oatpp/network/monitor/ConnectionMonitor.i
	@echo ... oatpp/network/monitor/ConnectionMonitor.s
	@echo ... oatpp/network/tcp/Connection.obj
	@echo ... oatpp/network/tcp/Connection.i
	@echo ... oatpp/network/tcp/Connection.s
	@echo ... oatpp/network/tcp/client/ConnectionProvider.obj
	@echo ... oatpp/network/tcp/client/ConnectionProvider.i
	@echo ... oatpp/network/tcp/client/ConnectionProvider.s
	@echo ... oatpp/network/tcp/server/ConnectionProvider.obj
	@echo ... oatpp/network/tcp/server/ConnectionProvider.i
	@echo ... oatpp/network/tcp/server/ConnectionProvider.s
	@echo ... oatpp/network/virtual_/Interface.obj
	@echo ... oatpp/network/virtual_/Interface.i
	@echo ... oatpp/network/virtual_/Interface.s
	@echo ... oatpp/network/virtual_/Pipe.obj
	@echo ... oatpp/network/virtual_/Pipe.i
	@echo ... oatpp/network/virtual_/Pipe.s
	@echo ... oatpp/network/virtual_/Socket.obj
	@echo ... oatpp/network/virtual_/Socket.i
	@echo ... oatpp/network/virtual_/Socket.s
	@echo ... oatpp/network/virtual_/client/ConnectionProvider.obj
	@echo ... oatpp/network/virtual_/client/ConnectionProvider.i
	@echo ... oatpp/network/virtual_/client/ConnectionProvider.s
	@echo ... oatpp/network/virtual_/server/ConnectionProvider.obj
	@echo ... oatpp/network/virtual_/server/ConnectionProvider.i
	@echo ... oatpp/network/virtual_/server/ConnectionProvider.s
	@echo ... oatpp/orm/DbClient.obj
	@echo ... oatpp/orm/DbClient.i
	@echo ... oatpp/orm/DbClient.s
	@echo ... oatpp/orm/Executor.obj
	@echo ... oatpp/orm/Executor.i
	@echo ... oatpp/orm/Executor.s
	@echo ... oatpp/orm/QueryResult.obj
	@echo ... oatpp/orm/QueryResult.i
	@echo ... oatpp/orm/QueryResult.s
	@echo ... oatpp/orm/SchemaMigration.obj
	@echo ... oatpp/orm/SchemaMigration.i
	@echo ... oatpp/orm/SchemaMigration.s
	@echo ... oatpp/orm/Transaction.obj
	@echo ... oatpp/orm/Transaction.i
	@echo ... oatpp/orm/Transaction.s
	@echo ... oatpp/parser/json/Beautifier.obj
	@echo ... oatpp/parser/json/Beautifier.i
	@echo ... oatpp/parser/json/Beautifier.s
	@echo ... oatpp/parser/json/Utils.obj
	@echo ... oatpp/parser/json/Utils.i
	@echo ... oatpp/parser/json/Utils.s
	@echo ... oatpp/parser/json/mapping/Deserializer.obj
	@echo ... oatpp/parser/json/mapping/Deserializer.i
	@echo ... oatpp/parser/json/mapping/Deserializer.s
	@echo ... oatpp/parser/json/mapping/ObjectMapper.obj
	@echo ... oatpp/parser/json/mapping/ObjectMapper.i
	@echo ... oatpp/parser/json/mapping/ObjectMapper.s
	@echo ... oatpp/parser/json/mapping/Serializer.obj
	@echo ... oatpp/parser/json/mapping/Serializer.i
	@echo ... oatpp/parser/json/mapping/Serializer.s
	@echo ... oatpp/web/client/ApiClient.obj
	@echo ... oatpp/web/client/ApiClient.i
	@echo ... oatpp/web/client/ApiClient.s
	@echo ... oatpp/web/client/HttpRequestExecutor.obj
	@echo ... oatpp/web/client/HttpRequestExecutor.i
	@echo ... oatpp/web/client/HttpRequestExecutor.s
	@echo ... oatpp/web/client/RequestExecutor.obj
	@echo ... oatpp/web/client/RequestExecutor.i
	@echo ... oatpp/web/client/RequestExecutor.s
	@echo ... oatpp/web/client/RetryPolicy.obj
	@echo ... oatpp/web/client/RetryPolicy.i
	@echo ... oatpp/web/client/RetryPolicy.s
	@echo ... oatpp/web/mime/multipart/FileProvider.obj
	@echo ... oatpp/web/mime/multipart/FileProvider.i
	@echo ... oatpp/web/mime/multipart/FileProvider.s
	@echo ... oatpp/web/mime/multipart/InMemoryDataProvider.obj
	@echo ... oatpp/web/mime/multipart/InMemoryDataProvider.i
	@echo ... oatpp/web/mime/multipart/InMemoryDataProvider.s
	@echo ... oatpp/web/mime/multipart/Multipart.obj
	@echo ... oatpp/web/mime/multipart/Multipart.i
	@echo ... oatpp/web/mime/multipart/Multipart.s
	@echo ... oatpp/web/mime/multipart/Part.obj
	@echo ... oatpp/web/mime/multipart/Part.i
	@echo ... oatpp/web/mime/multipart/Part.s
	@echo ... oatpp/web/mime/multipart/PartList.obj
	@echo ... oatpp/web/mime/multipart/PartList.i
	@echo ... oatpp/web/mime/multipart/PartList.s
	@echo ... oatpp/web/mime/multipart/PartReader.obj
	@echo ... oatpp/web/mime/multipart/PartReader.i
	@echo ... oatpp/web/mime/multipart/PartReader.s
	@echo ... oatpp/web/mime/multipart/Reader.obj
	@echo ... oatpp/web/mime/multipart/Reader.i
	@echo ... oatpp/web/mime/multipart/Reader.s
	@echo ... oatpp/web/mime/multipart/StatefulParser.obj
	@echo ... oatpp/web/mime/multipart/StatefulParser.i
	@echo ... oatpp/web/mime/multipart/StatefulParser.s
	@echo ... oatpp/web/mime/multipart/TemporaryFileProvider.obj
	@echo ... oatpp/web/mime/multipart/TemporaryFileProvider.i
	@echo ... oatpp/web/mime/multipart/TemporaryFileProvider.s
	@echo ... oatpp/web/protocol/CommunicationError.obj
	@echo ... oatpp/web/protocol/CommunicationError.i
	@echo ... oatpp/web/protocol/CommunicationError.s
	@echo ... oatpp/web/protocol/http/Http.obj
	@echo ... oatpp/web/protocol/http/Http.i
	@echo ... oatpp/web/protocol/http/Http.s
	@echo ... oatpp/web/protocol/http/encoding/Chunked.obj
	@echo ... oatpp/web/protocol/http/encoding/Chunked.i
	@echo ... oatpp/web/protocol/http/encoding/Chunked.s
	@echo ... oatpp/web/protocol/http/encoding/ProviderCollection.obj
	@echo ... oatpp/web/protocol/http/encoding/ProviderCollection.i
	@echo ... oatpp/web/protocol/http/encoding/ProviderCollection.s
	@echo ... oatpp/web/protocol/http/incoming/BodyDecoder.obj
	@echo ... oatpp/web/protocol/http/incoming/BodyDecoder.i
	@echo ... oatpp/web/protocol/http/incoming/BodyDecoder.s
	@echo ... oatpp/web/protocol/http/incoming/Request.obj
	@echo ... oatpp/web/protocol/http/incoming/Request.i
	@echo ... oatpp/web/protocol/http/incoming/Request.s
	@echo ... oatpp/web/protocol/http/incoming/RequestHeadersReader.obj
	@echo ... oatpp/web/protocol/http/incoming/RequestHeadersReader.i
	@echo ... oatpp/web/protocol/http/incoming/RequestHeadersReader.s
	@echo ... oatpp/web/protocol/http/incoming/Response.obj
	@echo ... oatpp/web/protocol/http/incoming/Response.i
	@echo ... oatpp/web/protocol/http/incoming/Response.s
	@echo ... oatpp/web/protocol/http/incoming/ResponseHeadersReader.obj
	@echo ... oatpp/web/protocol/http/incoming/ResponseHeadersReader.i
	@echo ... oatpp/web/protocol/http/incoming/ResponseHeadersReader.s
	@echo ... oatpp/web/protocol/http/incoming/SimpleBodyDecoder.obj
	@echo ... oatpp/web/protocol/http/incoming/SimpleBodyDecoder.i
	@echo ... oatpp/web/protocol/http/incoming/SimpleBodyDecoder.s
	@echo ... oatpp/web/protocol/http/outgoing/Body.obj
	@echo ... oatpp/web/protocol/http/outgoing/Body.i
	@echo ... oatpp/web/protocol/http/outgoing/Body.s
	@echo ... oatpp/web/protocol/http/outgoing/BufferBody.obj
	@echo ... oatpp/web/protocol/http/outgoing/BufferBody.i
	@echo ... oatpp/web/protocol/http/outgoing/BufferBody.s
	@echo ... oatpp/web/protocol/http/outgoing/MultipartBody.obj
	@echo ... oatpp/web/protocol/http/outgoing/MultipartBody.i
	@echo ... oatpp/web/protocol/http/outgoing/MultipartBody.s
	@echo ... oatpp/web/protocol/http/outgoing/Request.obj
	@echo ... oatpp/web/protocol/http/outgoing/Request.i
	@echo ... oatpp/web/protocol/http/outgoing/Request.s
	@echo ... oatpp/web/protocol/http/outgoing/Response.obj
	@echo ... oatpp/web/protocol/http/outgoing/Response.i
	@echo ... oatpp/web/protocol/http/outgoing/Response.s
	@echo ... oatpp/web/protocol/http/outgoing/ResponseFactory.obj
	@echo ... oatpp/web/protocol/http/outgoing/ResponseFactory.i
	@echo ... oatpp/web/protocol/http/outgoing/ResponseFactory.s
	@echo ... oatpp/web/protocol/http/outgoing/StreamingBody.obj
	@echo ... oatpp/web/protocol/http/outgoing/StreamingBody.i
	@echo ... oatpp/web/protocol/http/outgoing/StreamingBody.s
	@echo ... oatpp/web/protocol/http/utils/CommunicationUtils.obj
	@echo ... oatpp/web/protocol/http/utils/CommunicationUtils.i
	@echo ... oatpp/web/protocol/http/utils/CommunicationUtils.s
	@echo ... oatpp/web/server/AsyncHttpConnectionHandler.obj
	@echo ... oatpp/web/server/AsyncHttpConnectionHandler.i
	@echo ... oatpp/web/server/AsyncHttpConnectionHandler.s
	@echo ... oatpp/web/server/HttpConnectionHandler.obj
	@echo ... oatpp/web/server/HttpConnectionHandler.i
	@echo ... oatpp/web/server/HttpConnectionHandler.s
	@echo ... oatpp/web/server/HttpProcessor.obj
	@echo ... oatpp/web/server/HttpProcessor.i
	@echo ... oatpp/web/server/HttpProcessor.s
	@echo ... oatpp/web/server/HttpRouter.obj
	@echo ... oatpp/web/server/HttpRouter.i
	@echo ... oatpp/web/server/HttpRouter.s
	@echo ... oatpp/web/server/api/ApiController.obj
	@echo ... oatpp/web/server/api/ApiController.i
	@echo ... oatpp/web/server/api/ApiController.s
	@echo ... oatpp/web/server/api/Endpoint.obj
	@echo ... oatpp/web/server/api/Endpoint.i
	@echo ... oatpp/web/server/api/Endpoint.s
	@echo ... oatpp/web/server/handler/AuthorizationHandler.obj
	@echo ... oatpp/web/server/handler/AuthorizationHandler.i
	@echo ... oatpp/web/server/handler/AuthorizationHandler.s
	@echo ... oatpp/web/server/handler/ErrorHandler.obj
	@echo ... oatpp/web/server/handler/ErrorHandler.i
	@echo ... oatpp/web/server/handler/ErrorHandler.s
	@echo ... oatpp/web/server/interceptor/AllowCorsGlobal.obj
	@echo ... oatpp/web/server/interceptor/AllowCorsGlobal.i
	@echo ... oatpp/web/server/interceptor/AllowCorsGlobal.s
	@echo ... oatpp/web/url/mapping/Pattern.obj
	@echo ... oatpp/web/url/mapping/Pattern.i
	@echo ... oatpp/web/url/mapping/Pattern.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\MiWebApp\WebServerApp\build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

