#include "oatpp/network/Server.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/Address.hpp"
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/web/server/HttpRouter.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/server/handler/ErrorHandler.hpp"
#include "oatpp/web/protocol/http/outgoing/BufferBody.hpp"

// Include controllers and handlers
#include "SingerController.hpp"
#include "CustomErrorHandler.hpp"
#include "DatabaseService.hpp"


// Include database components
// #include "SimpleSQLiteService.hpp"

class AppComponent {
public:
  /////////////////////////////////////////////////////----HTTP ROUTER----//////////////////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, httpRouter)([] {
    return oatpp::web::server::HttpRouter::createShared();
  }());

  //////////////////////////////////////////////////----SERVER CONNECTION PROVIDER----////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, serverConnectionProvider)([] {
    return oatpp::network::tcp::server::ConnectionProvider::createShared({ "0.0.0.0", 8000, oatpp::network::Address::IP_4 });
  }());

  //////////////////////////////////////////////////----HTTP CONNECTION HANDLER----////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, serverConnectionHandler)([] {
    OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);
    auto connectionHandler = oatpp::web::server::HttpConnectionHandler::createShared(router);
    connectionHandler->setErrorHandler(std::make_shared<CustomErrorHandler>());
    return connectionHandler;
  }());

  //////////////////////////////////////////////////----DATABASE CONNECTION----////////////////////////////////////////
      OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionPool>, dbConnectionPool)([] {
        oatpp::sqlite::ConnectionPool::Config config;
        config.connectionString = "file::memory:?cache=shared";
        config.minConnections = 1;
        config.maxConnections = 10;
        config.connectionTimeoutMs = 5000;
        return oatpp::sqlite::ConnectionPool::createShared(config);
    }());
    //////////////////////////////////////////////////----DATABASE EXECUTOR----////////////////////////////////////////
    OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::sqlite::Executor>, dbExecutor)
    ([] {
        OATPP_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionPool>, connectionPool);
        return oatpp::sqlite::Executor::createShared(connectionPool);
    }());
};

void run() {
  AppComponent components;

  // Get components (httpRouter, serverConnectionProvider, serverConnectionHandler)
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, CRouter);
  OATPP_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, CConnectionProvider);
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, CConnectionHandler);

  // Create and add controllers
  auto mObjectMapper = oatpp::parser::json::mapping::ObjectMapper::createShared();

  auto mSingerController = std::make_shared<SingerController>(mObjectMapper);
  CRouter->addController(mSingerController);

  // auto databaseController = std::make_shared<DatabaseController>(objectMapper, databaseService);
  // router->addController(databaseController);

  // Create and run server
  oatpp::network::Server server(CConnectionProvider, CConnectionHandler);
  OATPP_LOGI("MiWebServer", "Running on port %s...", CConnectionProvider->getProperty("port").toString()->c_str());
  OATPP_LOGI("MiWebServer", "Server identifier: MiWebServer/1.0.0");
  server.run();
}

int main(int argc, const char* argv[]) {
  oatpp::base::Environment::init();
  run();
  oatpp::base::Environment::destroy();
  return 0;
}


