{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "4.0.3"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "Oat++", "jsonFile": "directory-Oat++-Debug-ebf42ee1e4f2c30924a0.json", "minimumCMakeVersion": {"string": "4.0.3"}, "parentIndex": 0, "projectIndex": 0, "source": "Oat++", "targetIndexes": [1]}, {"build": "Extensions/Oatpp-Sqlite", "jsonFile": "directory-Extensions.Oatpp-Sqlite-Debug-159271c5888eeee15547.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "Extensions/Oatpp-Sqlite", "targetIndexes": [2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "MiWebServer", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "MiWebServer::@6890427a1f51a3e7e1df", "jsonFile": "target-MiWebServer-Debug-88452684247adc4db957.json", "name": "MiWebServer", "projectIndex": 0}, {"directoryIndex": 1, "id": "oatpp::@103db6bc44d64c5e8dd4", "jsonFile": "target-oatpp-Debug-2b699d42b37595049d24.json", "name": "oatpp", "projectIndex": 0}, {"directoryIndex": 2, "id": "oatpp-sqlite::@a6bb91110ae3f8dbd25e", "jsonFile": "target-oatpp-sqlite-Debug-496fad0cf4b21f4d4c40.json", "name": "oatpp-sqlite", "projectIndex": 0}, {"directoryIndex": 2, "id": "sqlite::@a6bb91110ae3f8dbd25e", "jsonFile": "target-sqlite-Debug-0ceeaab40b8efda3a6fe.json", "name": "sqlite", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/MiWebApp/WebServerApp/out/build/AMD", "source": "D:/MiWebApp/WebServerApp"}, "version": {"major": 2, "minor": 8}}