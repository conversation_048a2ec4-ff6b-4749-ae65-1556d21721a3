{"archive": {}, "artifacts": [{"path": "Oat++/liboatpp.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_include_directories", "set_target_properties"], "files": ["Oat++/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 1, "file": 0, "line": 332, "parent": 0}, {"command": 2, "file": 0, "line": 300, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=c++11"}], "includes": [{"backtrace": 2, "path": "D:/MiWebApp/WebServerApp/Oat++"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "11"}, "sourceIndexes": [0, 22, 25, 27, 29, 31, 33, 35, 37, 40, 41, 42, 43, 45, 47, 49, 51, 54, 56, 59, 61, 63, 65, 67, 69, 71, 73, 75, 78, 80, 83, 85, 87, 89, 91, 93, 95, 97, 99, 102, 105, 107, 109, 111, 113, 115, 117, 122, 124, 129, 131, 133, 135, 137, 139, 141, 143, 145, 148, 150, 152, 154, 156, 158, 160, 162, 166, 169, 171, 173, 175, 177, 179, 181, 184, 186, 188, 190, 192, 194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, 234, 237, 239, 241, 243, 245, 247, 249, 251, 253, 255, 257, 259, 261, 263, 265, 267, 269, 271, 274, 276, 278, 280, 282, 284, 288]}], "id": "oatpp::@103db6bc44d64c5e8dd4", "name": "oatpp", "nameOnDisk": "liboatpp.a", "paths": {"build": "Oat++", "source": "Oat++"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 22, 25, 27, 29, 31, 33, 35, 37, 40, 41, 42, 43, 45, 47, 49, 51, 54, 56, 59, 61, 63, 65, 67, 69, 71, 73, 75, 78, 80, 83, 85, 87, 89, 91, 93, 95, 97, 99, 102, 105, 107, 109, 111, 113, 115, 117, 122, 124, 129, 131, 133, 135, 137, 139, 141, 143, 145, 148, 150, 152, 154, 156, 158, 160, 162, 166, 169, 171, 173, 175, 177, 179, 181, 184, 186, 188, 190, 192, 194, 196, 198, 200, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, 234, 237, 239, 241, 243, 245, 247, 249, 251, 253, 255, 257, 259, 261, 263, 265, 267, 269, 271, 274, 276, 278, 280, 282, 284, 288]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 26, 28, 30, 32, 34, 36, 38, 39, 44, 46, 48, 50, 52, 53, 55, 57, 58, 60, 62, 64, 66, 68, 70, 72, 74, 76, 77, 79, 81, 82, 84, 86, 88, 90, 92, 94, 96, 98, 100, 101, 103, 104, 106, 108, 110, 112, 114, 116, 118, 119, 120, 121, 123, 125, 126, 127, 128, 130, 132, 134, 136, 138, 140, 142, 144, 146, 147, 149, 151, 153, 155, 157, 159, 161, 163, 164, 165, 167, 168, 170, 172, 174, 176, 178, 180, 182, 183, 185, 187, 189, 191, 193, 195, 197, 199, 201, 203, 205, 207, 209, 211, 213, 215, 217, 219, 221, 223, 225, 227, 229, 231, 233, 235, 236, 238, 240, 242, 244, 246, 248, 250, 252, 254, 256, 258, 260, 262, 264, 266, 268, 270, 272, 273, 275, 277, 279, 281, 283, 285, 286, 287, 289, 290]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/algorithm/CRC.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/algorithm/CRC.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/ApiClient_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/ApiClient_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/ApiController_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/ApiController_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/DbClient_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/DbClient_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/DTO_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/DTO_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/auth_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/auth_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/base_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/base_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/bundle_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/bundle_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/cors_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/api_controller/cors_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/dto/base_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/dto/base_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/dto/enum_define.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/codegen/dto/enum_undef.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/IODefinitions.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/IODefinitions.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/Types.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/ConditionVariable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/ConditionVariable.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/Coroutine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/Coroutine.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/CoroutineWaitList.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/CoroutineWaitList.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/Error.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/Error.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/Executor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/Executor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/Lock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/Lock.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/Processor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/Processor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/utils/FastQueue.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/IOEventWorker_common.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/IOEventWorker_epoll.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/IOEventWorker_kqueue.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/IOEventWorker_stub.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/worker/IOEventWorker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/IOWorker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/worker/IOWorker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/TimerWorker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/worker/TimerWorker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/async/worker/Worker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/async/worker/Worker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/base/CommandLineArguments.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/base/CommandLineArguments.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/base/Config.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/base/Countable.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/base/Countable.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/base/Environment.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/base/Environment.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/base/ObjectHandle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/concurrency/SpinLock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/concurrency/SpinLock.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/concurrency/Thread.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/concurrency/Thread.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/Bundle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/Bundle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/buffer/FIFOBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/buffer/FIFOBuffer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/buffer/IOBuffer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/buffer/IOBuffer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/buffer/Processor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/buffer/Processor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/ObjectMapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/ObjectMapper.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/TypeResolver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/TypeResolver.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Any.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Any.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Collection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Enum.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Enum.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/List.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/List.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Map.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Object.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Object.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/PairList.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/PairList.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Primitive.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Primitive.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Type.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Type.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/UnorderedMap.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/UnorderedMap.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/UnorderedSet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/UnorderedSet.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/mapping/type/Vector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/mapping/type/Vector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/resource/File.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/resource/File.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/resource/InMemoryData.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/resource/InMemoryData.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/resource/Resource.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/resource/TemporaryFile.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/resource/TemporaryFile.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/share/LazyStringMap.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/share/MemoryLabel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/share/MemoryLabel.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/share/StringTemplate.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/share/StringTemplate.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/stream/BufferStream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/stream/BufferStream.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/stream/FIFOStream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/stream/FIFOStream.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/stream/FileStream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/stream/FileStream.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/stream/Stream.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/stream/Stream.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/data/stream/StreamBufferedProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/data/stream/StreamBufferedProxy.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/macro/basic.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/macro/codegen.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/macro/component.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/parser/Caret.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/parser/Caret.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/parser/ParsingError.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/parser/ParsingError.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/provider/Invalidator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/provider/Pool.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/core/provider/Provider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/utils/Binary.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/utils/Binary.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/utils/ConversionUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/utils/ConversionUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/utils/Random.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/utils/Random.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/core/utils/String.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/core/utils/String.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/encoding/Base64.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/encoding/Base64.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/encoding/Hex.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/encoding/Hex.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/encoding/Unicode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/encoding/Unicode.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/encoding/Url.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/encoding/Url.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/Address.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/Address.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/network/ConnectionHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/ConnectionPool.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/ConnectionPool.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/ConnectionProviderSwitch.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/ConnectionProviderSwitch.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/Server.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/Server.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/Url.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/Url.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/monitor/ConnectionInactivityChecker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/monitor/ConnectionInactivityChecker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/monitor/ConnectionMaxAgeChecker.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/monitor/ConnectionMaxAgeChecker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/monitor/ConnectionMonitor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/monitor/ConnectionMonitor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/network/monitor/MetricsChecker.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/network/monitor/StatCollector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/tcp/Connection.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/tcp/Connection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/network/tcp/ConnectionConfigurer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/tcp/client/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/tcp/client/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/tcp/server/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/tcp/server/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/virtual_/Interface.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/virtual_/Interface.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/virtual_/Pipe.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/virtual_/Pipe.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/virtual_/Socket.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/virtual_/Socket.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/virtual_/client/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/virtual_/client/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/network/virtual_/server/ConnectionProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/network/virtual_/server/ConnectionProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/orm/Connection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/orm/DbClient.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/orm/DbClient.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/orm/Executor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/orm/Executor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/orm/QueryResult.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/orm/QueryResult.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/orm/SchemaMigration.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/orm/SchemaMigration.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/orm/Transaction.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/orm/Transaction.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/parser/json/Beautifier.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/parser/json/Beautifier.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/parser/json/Utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/parser/json/Utils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/parser/json/mapping/Deserializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/parser/json/mapping/Deserializer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/parser/json/mapping/ObjectMapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/parser/json/mapping/ObjectMapper.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/parser/json/mapping/Serializer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/parser/json/mapping/Serializer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/client/ApiClient.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/client/ApiClient.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/client/HttpRequestExecutor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/client/HttpRequestExecutor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/client/RequestExecutor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/client/RequestExecutor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/client/RetryPolicy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/client/RetryPolicy.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/FileProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/FileProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/InMemoryDataProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/InMemoryDataProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/Multipart.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/Multipart.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/Part.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/Part.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/PartList.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/PartList.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/PartReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/PartReader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/Reader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/Reader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/StatefulParser.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/StatefulParser.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/mime/multipart/TemporaryFileProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/mime/multipart/TemporaryFileProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/CommunicationError.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/CommunicationError.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/Http.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/Http.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/encoding/Chunked.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/encoding/Chunked.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/encoding/EncoderProvider.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/encoding/ProviderCollection.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/encoding/ProviderCollection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/BodyDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/BodyDecoder.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/Request.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/Request.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/RequestHeadersReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/RequestHeadersReader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/Response.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/Response.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/ResponseHeadersReader.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/ResponseHeadersReader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/incoming/SimpleBodyDecoder.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/Body.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/Body.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/BufferBody.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/BufferBody.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/MultipartBody.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/MultipartBody.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/Request.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/Request.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/Response.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/Response.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/ResponseFactory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/ResponseFactory.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/outgoing/StreamingBody.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/outgoing/StreamingBody.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/protocol/http/utils/CommunicationUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/protocol/http/utils/CommunicationUtils.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/AsyncHttpConnectionHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/AsyncHttpConnectionHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/HttpConnectionHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/HttpConnectionHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/HttpProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/HttpProcessor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/HttpRequestHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/HttpRouter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/HttpRouter.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/api/ApiController.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/api/ApiController.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/api/Endpoint.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/api/Endpoint.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/handler/AuthorizationHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/handler/AuthorizationHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/handler/ErrorHandler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/handler/ErrorHandler.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/server/interceptor/AllowCorsGlobal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/interceptor/AllowCorsGlobal.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/interceptor/RequestInterceptor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/web/server/interceptor/ResponseInterceptor.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Oat++/oatpp/web/url/mapping/Pattern.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Oat++/oatpp/web/url/mapping/Pattern.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Oat++/oatpp/web/url/mapping/Router.hpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}