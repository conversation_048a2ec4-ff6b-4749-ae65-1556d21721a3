{"archive": {}, "artifacts": [{"path": "Extensions/Oatpp-Sqlite/libsqlite.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_include_directories"], "files": ["Extensions/Oatpp-Sqlite/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 4, "parent": 0}, {"command": 1, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}], "includes": [{"backtrace": 2, "path": "D:/MiWebApp/WebServerApp/Extensions/Oatpp-Sqlite/sqlite"}], "language": "C", "sourceIndexes": [0]}], "id": "sqlite::@a6bb91110ae3f8dbd25e", "name": "sqlite", "nameOnDisk": "libsqlite.a", "paths": {"build": "Extensions/Oatpp-Sqlite", "source": "Extensions/Oatpp-Sqlite"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Extensions/Oatpp-Sqlite/sqlite/sqlite3.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Extensions/Oatpp-Sqlite/sqlite/sqlite3.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}